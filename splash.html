<!DOCTYPE html>
<html>
<head>
    <style>
        body {
            margin: 0;
            background: transparent;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
        }

        .splash-content {
            text-align: center;
        }

        .logo {
            width: 200px;
            animation: pulse 2s infinite;
        }

        .loading-bar {
            width: 200px;
            height: 4px;
            background: rgba(255,255,255,0.2);
            margin: 20px auto;
            position: relative;
        }

        .loading-progress {
            width: 0%;
            height: 100%;
            background: #2196F3;
            transition: width 0.3s;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
<div class="splash-content">
    <img src="10001.gif" class="logo" alt="App Logo">
    <div class="loading-bar">
        <div class="loading-progress" id="progress"></div>
    </div>
    <p style="color: white;">简单直播正在启动...</p>
</div>

<script>
    // 模拟进度条动画（实际项目可绑定真实加载进度）
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 10;
        document.getElementById('progress').style.width = `${progress}%`;
        if (progress >= 100) clearInterval(interval);
    }, 300);
</script>
</body>
</html>
