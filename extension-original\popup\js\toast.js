/**
 * 自定义弹窗系统
 * 替代浏览器默认alert，并在2秒后自动消失
 */

// 创建toast消息
function showToast(message, type = 'info') {
    const container = document.getElementById('toast-container');
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.className = `toast ${type}`;
    
    // 根据类型设置图标
    let icon = '';
    switch(type) {
        case 'success':
            icon = '<i class="fas fa-check-circle toast-icon"></i>';
            break;
        case 'error':
            icon = '<i class="fas fa-exclamation-circle toast-icon"></i>';
            break;
        case 'warning':
            icon = '<i class="fas fa-exclamation-triangle toast-icon"></i>';
            break;
        case 'info':
        default:
            icon = '<i class="fas fa-info-circle toast-icon"></i>';
            break;
    }
    
    // 设置内容
    toast.innerHTML = `
        ${icon}
        <div class="toast-message">${message}</div>
        <div class="toast-progress"></div>
    `;
    
    // 添加到容器
    container.appendChild(toast);
    
    // 2秒后自动移除
    setTimeout(() => {
        toast.style.animation = 'toast-out 0.3s forwards';
        
        // 等待动画完成后移除元素
        setTimeout(() => {
            container.removeChild(toast);
        }, 300);
    }, 2000);
}

// 替换所有原生alert
window.originalAlert = window.alert;
window.alert = function(message) {
    // 根据消息内容判断类型
    let type = 'info';
    
    if (typeof message === 'string') {
        const lowerMsg = message.toLowerCase();
        if (lowerMsg.includes('成功')) {
            type = 'success';
        } else if (lowerMsg.includes('失败') || lowerMsg.includes('错误') || lowerMsg.includes('请输入正确') || lowerMsg.includes('请至少')) {
            type = 'error';
        } else if (lowerMsg.includes('警告') || lowerMsg.includes('注意')) {
            type = 'warning';
        }
    }
    
    showToast(message, type);
    
    // 记录到控制台，便于调试
    console.log(`[Toast ${type}]`, message);
};

// 导出辅助函数，用于直接指定类型
window.toast = {
    success: (message) => showToast(message, 'success'),
    error: (message) => showToast(message, 'error'),
    warning: (message) => showToast(message, 'warning'),
    info: (message) => showToast(message, 'info')
}; 