// 插件窗口预加载脚本
const { ipcRenderer } = require('electron');

// 当页面加载完成后，将设备指纹存储到localStorage
window.addEventListener("DOMContentLoaded", async () => {
  if (localStorage.getItem("device_fingerprint")) {
    return;
  }
  try {
    // 从主进程获取设备指纹
    const deviceFingerprint = await ipcRenderer.invoke('get-device-fingerprint');
    // 存储到localStorage
    localStorage.setItem(
      "device_fingerprint",
      JSON.stringify(deviceFingerprint)
    );
    console.log("设备指纹已存储到localStorage:", deviceFingerprint);
  } catch (error) {
    console.error("获取或存储设备指纹失败:", error);
  }
});
