<!DOCTYPE html>
<html lang="zh-cn">

<head>
    <meta charset="UTF-8">
    <link rel="stylesheet" href="css/popup.css">
    <link rel="stylesheet" href="css/all.min.css">
    <title>简单直播助手</title>
    <script src="js/qrcode.min.js"></script>
</head>

<body>
    <!-- 登录框 -->
    <div id="login-container" class="login-container">
        <div class="login-box">
            <h2>用户登录</h2>
            <div class="login-content">
                <div class="input-group">
                    <label for="phone"><i class="fas fa-mobile-alt"></i></label>
                    <input type="text" id="phone" placeholder="请输入手机号" maxlength="11" />
                </div>
                <div class="input-group code-group">
                    <label for="verifyCode"><i class="fas fa-key"></i></label>
                    <input type="text" id="verifyCode" placeholder="请输入验证码" maxlength="6" />
                    <button id="sendCodeBtn">获取验证码</button>
                </div>
                <button id="loginBtn" class="login-btn"><i class="fas fa-sign-in-alt"></i> 登录/注册</button>
                <div class="agreement-group">
                    <input type="checkbox" id="agreement" />
                    <label for="agreement">我已阅读并同意<a href="#" id="showAgreement">《用户协议》</a></label>
                </div>
                <p class="login-tip">未注册用户将自动完成注册</p>
            </div>
        </div>
    </div>

    <div class="tab-container" style="display: none; flex-direction: column;">
        <!-- 简化后的个人中心入口 -->
        <div class="profile-header">
            <button id="profileBtn" class="profile-btn"><i class="fas fa-user-cog"></i> 个人中心</button>
            <div class="profile-actions">
                <div class="expire-info">
                    <i class="fas fa-calendar-alt"></i>
                    <span id="expireDate">到期时间: 20xx-xx-xx</span>
                </div>
                <div class="settings-container">
                    <button id="settingsBtn" class="settings-btn"><i class="fas fa-cog"></i></button>
                </div>
            </div>
        </div>

        <div class="tabs">
            <button class="tab"><i class="fas fa-reply"></i> 滚动回复</button>
            <button class="tab"><i class="fas fa-comment-dots"></i> 关键词回复</button>
            <button class="tab"><i class="fas fa-shopping-cart"></i> 商品讲解</button>
        </div>

        <div class="tab-content">
            <!-- 自动回复内容 -->
            <div class="box">
                <h3>自动回复</h3>
                <div id="scrollStatus" class="status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">停止运行</span>
                </div>
                <div class="group">
                    <input type="file" id="autoReplayFile" accept="text/csv" /><a href="autoReply.csv"><i
                            class="fas fa-download"></i> 下载模板</a>
                </div>
                <div class="group" id="csvList">
                    <div class="group-item">
                        <input class="contentInfo" type="text" placeholder="请输入内容" />
                        <span class="delete-content">删除</span>
                    </div>
                </div>

                <!--            <div class="group">-->
                <!--                <label for="selector">CSS选择器</label>-->
                <!--                <input type="text" id="selector" placeholder="输入CSS选择器 (如 #btn)" />-->
                <!--            </div>-->

                <div class="group">
                    <label for="interval">间隔(单位:秒，低于10秒将无效)</label>
                    <input type="number" id="interval" value="30" min="30" placeholder="间隔时间(秒),低于10秒将无效" />
                </div>

                <div class="button-group">
                    <button class="btn" id="addContentBtn"><i class="fas fa-plus"></i> 新增</button>
                    <button class="btn" id="start"><i class="fas fa-play"></i> 开始</button>
                    <button class="btn" id="stop"><i class="fas fa-stop"></i> 停止</button>
                    <button class="btn" id="resetScrollBtn"><i class="fas fa-redo-alt"></i> 重置</button>
                </div>
            </div>
        </div>

        <div class="tab-content">
            <!-- 关键词回复内容 -->
            <div class="box qa-box">
                <h3>关键词回复</h3>
                <div id="keywordStatus" class="status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">停止运行</span>
                </div>
                <div class="group">
                    <input type="file" id="keywordReplayFile" accept="text/csv" /><a href="QAfile.csv"><i
                            class="fas fa-download"></i> 下载模板</a>
                </div>
                <!-- 隐藏的模板行（始终存在且不可见） -->
                <div class="qalist template" style="display: none;">
                    <input type="text" placeholder="请输入问题" />
                    <span><i class="fas fa-exchange-alt"></i></span>
                    <input type="text" placeholder="请输入自动回复内容" />
                    <span class="delete-qa">删除</span>
                </div>
                <div class="group qalist">
                    <input type="text" placeholder="请输入问题" />
                    <span><i class="fas fa-exchange-alt"></i></span>
                    <input type="text" placeholder="请输入自动回复内容" />
                    <span class="delete-qa">删除</span>
                </div>
                <div class="group qalist">
                    <input type="text" placeholder="请输入问题" />
                    <span><i class="fas fa-exchange-alt"></i></span>
                    <input type="text" placeholder="请输入自动回复内容" />
                    <span class="delete-qa">删除</span>
                </div>
                <div class="group qalist">
                    <input type="text" placeholder="请输入问题" />
                    <span><i class="fas fa-exchange-alt"></i></span>
                    <input type="text" placeholder="请输入自动回复内容" />
                    <span class="delete-qa">删除</span>
                </div>

                <button id="addQAButton"><i class="fas fa-plus"></i> 新增</button>
                <button id="saveQAButton"><i class="fas fa-save"></i> 确认</button>
                <button id="cancelQAButton"><i class="fas fa-times"></i> 停止</button>
                <button id="resetKeywordBtn"><i class="fas fa-redo-alt"></i> 重置</button>
            </div>
        </div>

        <div class="tab-content">
            <!-- 商品讲解内容 -->
            <div class="box">
                <h3>商品讲解</h3>
                <div id="productStatus" class="status-indicator">
                    <span class="status-dot"></span>
                    <span class="status-text">停止运行</span>
                </div>
                <div class="group">
                    <div class="select-all-container">
                        <div class="select-controls">
                            <button class="toggle-select-btn"><i class="fas fa-check-square"></i> 全选</button>
                            <button class="invert-select-btn"><i class="fas fa-exchange-alt"></i> 反选</button>
                            <button class="refresh-product-btn"><i class="fas fa-sync-alt"></i> 刷新</button>
                        </div>
                        <div class="selected-count">已选择: <span>0</span> 个商品</div>
                    </div>
                    <div class="product-list-container">
                        <!-- 商品列表将由JavaScript动态生成 -->
                    </div>
                </div>
                <div class="group">
                    <label>间隔(单位:秒，低于20秒将无效)</label>
                    <input type="number" id="productPlayTime" value="20" min="20" placeholder="间隔时间(秒),低于20秒将无效" />
                </div>
                <button id="productConfirm"><i class="fas fa-check"></i> 确认</button>
                <button id="productStop"><i class="fas fa-times"></i> 停止</button>
            </div>
        </div>

        <!-- 个人中心弹窗 -->
        <div id="profileModal" class="profile-modal">
            <div class="profile-modal-content">
                <div class="profile-modal-header">
                    <h3>个人中心</h3>
                    <span id="closeProfileModal" class="close-btn"><i class="fas fa-times"></i></span>
                </div>
                <div class="profile-modal-body">
                    <div class="user-phone-info">
                        <i class="fas fa-mobile-alt"></i>
                        <span id="userPhoneDisplay">138****1234</span>
                    </div>
                    <div class="package-list-title">套餐列表</div>
                    <div class="package-list">
                        <!-- 套餐列表项由JavaScript动态生成 -->
                        <div class="package-item">
                            <div class="package-info">
                                <h4>基础版</h4>
                                <div class="package-duration">包月</div>
                            </div>
                            <div class="package-price">¥29.9</div>
                            <button class="buy-btn">购买</button>
                        </div>
                        <div class="package-item">
                            <div class="package-info">
                                <h4>高级版</h4>
                                <div class="package-duration">包季</div>
                            </div>
                            <div class="package-price">¥79.9</div>
                            <button class="buy-btn">购买</button>
                        </div>
                        <div class="package-item">
                            <div class="package-info">
                                <h4>尊享版</h4>
                                <div class="package-duration">包年</div>
                            </div>
                            <div class="package-price">¥299.9</div>
                            <button class="buy-btn">购买</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支付二维码弹窗 -->
        <div id="paymentModal" class="payment-modal">
            <div class="payment-modal-content">
                <div class="payment-modal-header">
                    <h3>扫码支付</h3>
                    <span id="closePaymentModal" class="close-btn"><i class="fas fa-times"></i></span>
                </div>
                <div class="payment-modal-body">
                    <div class="payment-info">
                        <h4 id="packageName">尊享版</h4>
                        <div class="payment-price" id="packagePrice">¥299.9</div>
                    </div>
                    <div class="qrcode-container">
                        <div id="paymentQrcode" class="payment-qrcode">
                            <!-- 二维码图片会从服务器获取 -->
                            <div class="loading-spinner">
                                <div class="spinner"></div>
                                <p>正在加载支付二维码...</p>
                            </div>
                        </div>
                        <div class="payment-tips">
                            <p>请使用微信扫码完成支付</p>
                            <p>支付完成后，服务将自动开通</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置弹窗 -->
        <div id="settingsModal" class="settings-modal">
            <div class="settings-modal-content">
                <div class="settings-modal-header">
                    <h3>设置</h3>
                    <span id="closeSettingsModal" class="close-btn"><i class="fas fa-times"></i></span>
                </div>
                <div class="settings-modal-body">
                    <div class="settings-group">
                        <label for="replyText">回复</label>
                        <input type="text" id="replyText" placeholder="请输入" />
                    </div>
                    <div class="settings-group">
                        <label for="sendText">发送</label>
                        <input type="text" id="sendText" placeholder="请输入" />
                    </div>
                    <div class="settings-group">
                        <label for="productListText">产品列表</label>
                        <input type="text" id="productListText" placeholder="请输入" />
                    </div>
                    <div class="settings-group">
                        <label for="explanationText">讲解</label>
                        <input type="text" id="explanationText" placeholder="请输入" />
                    </div>
                    <div class="settings-buttons">
                        <button id="saveSettingsBtn"><i class="fas fa-save"></i> 保存</button>
                        <button id="resetSettingsBtn"><i class="fas fa-redo-alt"></i> 重置</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 续费提示弹窗 -->
        <div id="renewalModal" class="renewal-modal">
            <div class="renewal-modal-content">
                <div class="renewal-modal-header">
                    <h3>会员服务到期提醒</h3>
                    <span id="closeRenewalModal" class="close-btn"><i class="fas fa-times"></i></span>
                </div>
                <div class="renewal-modal-body">
                    <div class="renewal-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="renewal-message">
                        <p>您的会员服务已到期，无法继续使用</p>
                        <p>请续费以继续使用完整功能</p>
                    </div>
                    <div class="renewal-buttons">
                        <button id="cancelRenewalBtn" class="cancel-renewal-btn">暂不续费</button>
                        <button id="confirmRenewalBtn" class="confirm-renewal-btn">立即续费</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 自定义弹窗 -->
    <div id="toast-container" class="toast-container"></div>



    </div> <!-- 这是tab-container的结束标签 -->
    
    <script src="js/http.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/login.js"></script>
    <script src="js/popup.js"></script>
    <script src="js/keywords.replay.js"></script>
    <script src="js/tabs.js"></script>
    <script src="js/toast.js"></script>
    <script src="js/settings.js"></script>
</body>

</html>