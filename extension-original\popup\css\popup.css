body {
    width: 700px;
    height: 700px;
    font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    /* padding: 20px; */
    box-sizing: border-box;
    overflow-y: auto;
    color: #333;
}

.tab-container {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    /* border-radius: 10px; */
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    background-color: #fff;
    overflow: hidden;
}

.tabs {
    display: flex;
    background: linear-gradient(135deg, #43a047, #2e7d32);
    border-radius: 0;
    overflow: hidden;
    position: relative;
    padding: 5px;
    z-index: 1;
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
}

.tabs:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="100" height="100" fill="none" stroke="rgba(255,255,255,0.05)" stroke-width="2"/></svg>');
    opacity: 0.3;
    z-index: -1;
}

.tab {
    padding: 15px 25px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    font-size: 15px;
    font-weight: bold;
    color: rgba(255, 255, 255, 0.8);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    flex: 1;
    text-align: center;
    z-index: 1;
    margin: 0 3px;
    border-radius: 8px;
}

.tab i {
    margin-right: 8px;
    font-size: 16px;
    position: relative;
    top: 1px;
    transition: transform 0.3s ease;
}

.tab:hover i {
    transform: scale(1.2);
}

.tab:before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: #fff;
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.tab:after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.tab.active {
    background-color: rgba(255, 255, 255, 0.15);
    color: white;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    transform: translateY(-2px);
}

.tab.active:before {
    transform: scaleX(1);
}

.tab.active:after {
    opacity: 1;
}

.tab.active i {
    transform: scale(1.2);
}

.tab:hover:not(.active) {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateY(-1px);
}

.tab-content {
    display: none;
    padding: 25px;
    background-color: #fff;
    border-radius: 0 0 10px 10px;
    /* flex-grow: 1; */
    flex: 1;
    overflow-y: auto;
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.group {
    margin-bottom: 20px;
    position: relative;
}

h3 {
    color: #2e7d32;
    border-bottom: none;
    padding-bottom: 15px;
    margin-top: 0;
    margin-bottom: 25px;
    font-size: 20px;
    position: relative;
    text-align: center;
    font-weight: 600;
    letter-spacing: 1px;
}

h3:before {
    content: '';
    position: absolute;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(67, 160, 71, 0.5), transparent);
    bottom: 0;
    left: 0;
}

h3:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    width: 80px;
    height: 4px;
    background: linear-gradient(to right, #4CAF50, #2e7d32);
    transform: translateX(-50%);
    border-radius: 2px;
}

input[type="text"],
input[type="number"],
textarea {
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    width: 100%;
    box-sizing: border-box;
    font-size: 14px;
    transition: all 0.3s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    background-color: #f9f9f9;
}

input[type="text"]:focus,
input[type="number"]:focus,
textarea:focus {
    border-color: #43a047;
    box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
    outline: none;
    background-color: #fff;
}

input[type="file"] {
    margin-right: 10px;
    padding: 12px 0;
    position: relative;
    cursor: pointer;
}

input[type="file"]::-webkit-file-upload-button {
    background: linear-gradient(to right, #43a047, #2e7d32);
    color: white;
    padding: 10px 15px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
}

input[type="file"]::-webkit-file-upload-button:hover {
    background: linear-gradient(to right, #388e3c, #2e7d32);
    box-shadow: 0 2px 5px rgba(46, 125, 50, 0.3);
}

label:not(.product-label) {
    display: inline-block;
    width: auto;
    font-weight: 600;
    color: #2e7d32;
    margin-right: 15px;
    margin-bottom: 8px;
}

button {
    background: linear-gradient(to right, #43a047, #2e7d32);
    color: white;
    border: none;
    padding: 12px 24px;
    text-align: center;
    text-decoration: none;
    display: inline-block;
    font-size: 15px;
    margin: 6px 4px;
    cursor: pointer;
    border-radius: 8px;
    transition: all 0.3s;
    box-shadow: 0 3px 6px rgba(46, 125, 50, 0.2);
    position: relative;
    overflow: hidden;
    font-weight: 600;
    letter-spacing: 0.5px;
}

button:after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 255, 255, 0.5);
    opacity: 0;
    border-radius: 100%;
    transform: scale(1, 1) translate(-50%);
    transform-origin: 50% 50%;
}

button:hover {
    background: linear-gradient(to right, #388e3c, #1b5e20);
    box-shadow: 0 5px 10px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

button:focus {
    outline: none;
}

button:active {
    transform: translateY(1px);
    box-shadow: 0 2px 3px rgba(46, 125, 50, 0.2);
}

button:active:after {
    animation: ripple 0.6s ease-out;
}

button i {
    margin-right: 8px;
    font-size: 16px;
    position: relative;
    top: 1px;
}

@keyframes ripple {
    0% {
        transform: scale(0, 0);
        opacity: 0.5;
    }
    100% {
        transform: scale(20, 20);
        opacity: 0;
    }
}

ul {
    padding: 0;
    display: flex;
    flex-wrap: wrap;
}

ul li {
    list-style: none;
    display: inline-block;
    padding: 8px 12px;
    background: linear-gradient(to right, #43a047, #2e7d32);
    color: #ffffff;
    margin-right: 8px;
    margin-bottom: 8px;
    border-radius: 20px;
    font-size: 13px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s;
}

ul li:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.15);
}

.contentInfo {
    width: 100%;
    min-height: 50px;
    resize: vertical;
    padding-right: 60px; /* 为删除按钮留出空间 */
}
/* 最后一个不要下边距 */
.contentInfo:last-child {
    margin-bottom: 0;
}

.box {
    position: relative;
}

.qalist {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    padding: 15px 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    transition: all 0.3s;
    border: 1px solid rgba(67, 160, 71, 0.1);
}

.qalist:hover {
    background-color: #f1f8e9;
    box-shadow: 0 4px 8px rgba(0,0,0,0.05);
    transform: translateY(-2px);
    border-color: rgba(67, 160, 71, 0.3);
}

.qalist input {
    flex: 1;
    margin: 0 8px;
    background-color: transparent;
    border: none;
    border-bottom: 1px dashed rgba(67, 160, 71, 0.3);
    border-radius: 0;
    padding: 10px 5px;
    box-shadow: none;
}

.qalist input:focus {
    background-color: transparent;
    border-bottom: 1px solid #43a047;
    box-shadow: 0 1px 0 0 rgba(67, 160, 71, 0.2);
}

.qalist span {
    margin: 0 20px;
    color: #888;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 36px;
    height: 36px;
    background-color: #f1f8e9;
    border-radius: 50%;
    transition: all 0.3s;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.qalist:hover span:not(.delete-qa) {
    background-color: #43a047;
    color: white;
    transform: rotate(180deg);
    box-shadow: 0 4px 6px rgba(67, 160, 71, 0.3);
}

.qalist .delete-qa {
    cursor: pointer;
    color: #c62828;
    margin-left: 8px;
    opacity: 0.7;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 13px;
    background-color: rgba(198, 40, 40, 0.1);
}

.qalist:hover .delete-qa {
    opacity: 1;
    box-shadow: 0 4px 6px rgba(198, 40, 40, 0.2);
}

.qalist .delete-qa:hover {
    background-color: rgba(198, 40, 40, 0.2);
    color: #b71c1c;
}

a {
    text-decoration: none;
    color: #43a047;
    margin-left: 10px;
    font-weight: bold;
    transition: all 0.3s;
    position: relative;
}

a:hover {
    color: #2e7d32;
}

a:after {
    content: '';
    position: absolute;
    width: 100%;
    transform: scaleX(0);
    height: 2px;
    bottom: -2px;
    left: 0;
    background-color: #2e7d32;
    transform-origin: bottom right;
    transition: transform 0.3s ease-out;
}

a:hover:after {
    transform: scaleX(1);
    transform-origin: bottom left;
}

.product-list-container {
    max-height: 350px;
    overflow-y: auto;
    border: 1px solid #eaeaea;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
    background-color: #f9f9f9;
}

.no-product-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    text-align: center;
}

.no-product-message p {
    color: #757575;
    font-size: 16px;
    margin-bottom: 15px;
}

.get-product-btn {
    background: linear-gradient(135deg, #29B6F6, #0288D1);
    color: white;
    border: 1px solid #0288D1;
    border-radius: 6px;
    padding: 10px 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.get-product-btn i {
    margin-right: 8px;
}

.get-product-btn:hover {
    background: linear-gradient(135deg, #03A9F4, #0277BD);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.get-product-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.loading-indicator {
    text-align: center;
    padding: 20px;
    color: #666;
    font-size: 16px;
}

.loading-indicator i {
    margin-right: 10px;
    color: #43a047;
}

.select-all-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.select-controls {
    display: flex;
    gap: 10px;
}

.toggle-select-btn, .invert-select-btn, .refresh-product-btn {
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    margin-right: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid transparent;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    color: #fff;
    min-width: 80px;
}

.toggle-select-btn i, .invert-select-btn i, .refresh-product-btn i {
    margin-right: 6px;
    font-size: 14px;
}

.toggle-select-btn {
    background: linear-gradient(135deg, #4CAF50, #388E3C);
    border-color: #388E3C;
}

.toggle-select-btn:hover {
    background: linear-gradient(135deg, #43A047, #2E7D32);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.toggle-select-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.invert-select-btn {
    background: linear-gradient(135deg, #5C6BC0, #3949AB);
    border-color: #3949AB;
}

.invert-select-btn:hover {
    background: linear-gradient(135deg, #3F51B5, #303F9F);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.invert-select-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.refresh-product-btn {
    background: linear-gradient(135deg, #29B6F6, #0288D1);
    border-color: #0288D1;
}

.refresh-product-btn:hover {
    background: linear-gradient(135deg, #03A9F4, #0277BD);
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.refresh-product-btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.selected-count {
    background-color: #f1f8e9;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 14px;
    color: #2e7d32;
    font-weight: 600;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
}

.selected-count span {
    display: inline-block;
    min-width: 24px;
    text-align: center;
    background-color: #43a047;
    color: white;
    border-radius: 12px;
    padding: 2px 6px;
    margin-left: 5px;
    margin-right: 2px;
    font-size: 13px;
}

.product-list-container::-webkit-scrollbar {
    width: 8px;
}

.product-list-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.product-list-container::-webkit-scrollbar-thumb {
    background: #c8e6c9;
    border-radius: 4px;
}

.product-list-container::-webkit-scrollbar-thumb:hover {
    background: #81c784;
}

.product-item {
    padding: 10px 15px;
    margin-bottom: 8px;
    border-radius: 6px;
    background-color: white;
    display: flex;
    align-items: center;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.product-item:hover {
    background-color: #f1f8e9;
    border-color: #a5d6a7;
    transform: translateX(5px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.product-checkbox {
    margin-right: 10px;
    width: 18px;
    height: 18px;
    accent-color: #43a047;
    cursor: pointer;
}

.product-label {
    font-weight: normal;
    color: #333;
    cursor: pointer;
    flex: 1;
    margin: 0;
    transition: color 0.2s ease;
    display: flex;
    flex-direction: row;
    align-items: center;
}

.product-name {
    font-weight: 500;
    margin-bottom: 3px;
}

.product-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
    margin-right: 10px;
    border: 1px solid #e0e0e0;
    transition: all 0.2s ease;
}

.product-item:hover .product-image {
    border-color: #81c784;
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.product-item:hover .product-label {
    color: #2e7d32;
}

.product-checkbox:checked + .product-label {
    color: #2e7d32;
}

.product-checkbox:checked + .product-label .product-name {
    font-weight: 600;
}

/* 登录样式 */
.login-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
}

.login-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    width: 100%;
}

.login-box {
    background: white;
    border-radius: 6px;
    padding: 30px;
    width: 90%;
    max-width: 450px;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    display: flex;
    flex-direction: column;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(to bottom, #fff 0%, #f8f9fa 100%);
    position: relative;
    transform: translateY(-20px);
    animation: floatIn 0.5s forwards;
}

@keyframes floatIn {
    0% { opacity: 0; transform: translateY(30px); }
    100% { opacity: 1; transform: translateY(0); }
}

/* 添加装饰元素 */
.login-box:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(to right, #43a047, #2e7d32);
    border-radius: 0;
}

.login-box:after {
    content: '';
    position: absolute;
    top: 15px;
    right: 15px;
    width: 60px;
    height: 60px;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="60" height="60" viewBox="0 0 100 100"><rect x="10" y="10" width="80" height="80" fill="none" stroke="rgba(67, 160, 71, 0.1)" stroke-width="5"/></svg>');
    opacity: 0.5;
    pointer-events: none;
}

.login-box h2 {
    text-align: center;
    margin-bottom: 25px;
    color: #2e7d32;
    font-size: 22px;
    position: relative;
    padding-bottom: 12px;
    letter-spacing: 1.2px;
    text-transform: uppercase;
    font-weight: 600;
}

.login-box h2:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 50px;
    height: 2px;
    background: linear-gradient(to right, #4CAF50, #2e7d32);
    transform: translateX(-50%);
    border-radius: 0;
}

.input-group {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    width: 100%;
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
    transition: all 0.3s ease;
    position: relative;
}

.input-group:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.08);
    border-color: #c8e6c9;
}

.input-group:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(to right, #43a047, #2e7d32);
    transition: width 0.3s ease;
}

.input-group:hover:after {
    width: 100%;
}

.input-group label {
    background: #f5f5f5;
    padding: 12px;
    color: #2e7d32;
    border-right: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    margin-bottom: 0;
    margin-right: 0;
}

.input-group label i {
    font-size: 18px;
}

.input-group input {
    flex: 1;
    padding: 12px 15px;
    border: none;
    outline: none;
    font-size: 15px;
    background-color: transparent;
    box-shadow: none;
    border-radius: 0;
}

.code-group {
    position: relative;
}

#sendCodeBtn {
    position: absolute;
    right: 5px;
    top: 5px;
    bottom: 5px;
    padding: 0 15px;
    background: #f0f0f0;
    border: none;
    border-radius: 0;
    color: #2e7d32;
    cursor: pointer;
    white-space: nowrap;
    font-weight: 600;
    transition: all 0.3s;
    font-size: 13px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 0;
}

#sendCodeBtn:hover {
    background: #e0e0e0;
    color: #388e3c;
    box-shadow: 0 3px 6px rgba(0,0,0,0.15);
}

.login-btn {
    width: 100%;
    max-width: 400px;
    padding: 12px;
    background: linear-gradient(to right, #43a047, #2e7d32);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    margin: 15px auto;
    display: block;
    transition: all 0.3s;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 4px 6px rgba(46, 125, 50, 0.2);
}

.login-btn:hover {
    background: linear-gradient(to right, #388e3c, #1b5e20);
    box-shadow: 0 6px 10px rgba(46, 125, 50, 0.4);
    transform: translateY(-2px);
}

.login-tip {
    text-align: center;
    color: #666;
    font-size: 13px;
    margin-top: 15px;
    position: relative;
    padding-top: 15px;
}

.login-tip:before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 1px;
    background: #ddd;
}

/* 登录按钮禁用状态 */
.login-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

/* 输入验证样式 */
input.valid {
    border-color: #52c41a !important;
    background-color: #f6ffed;
}

input.invalid {
    border-color: #ff4d4f !important;
    background-color: #fff2f0;
}

.input-group.error {
    border-color: #ff4d4f;
}

.input-group.error:after {
    background: linear-gradient(to right, #ff4d4f, #f5222d);
    width: 100%;
}

.input-group.success {
    border-color: #52c41a;
}

.input-group.success:after {
    background: linear-gradient(to right, #52c41a, #389e0d);
    width: 100%;
}

/* 个人中心头部样式 */
.profile-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: #fff;
    /* border-radius: 10px 10px 0 0; */
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
}

.user-info i {
    font-size: 24px;
    color: #ecf0f1;
}

#username {
    font-weight: bold;
    font-size: 16px;
}

.profile-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.profile-btn {
    background: rgba(255, 255, 255, 0.15);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
}

.profile-btn i {
    font-size: 16px;
}

.expire-info {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    transition: all 0.3s;
    height: 36px;
    box-sizing: border-box;
}

.expire-info:hover {
    background: rgba(255, 255, 255, 0.15);
}

.expire-info i {
    color: #f1c40f;
}

#expireDate {
    font-weight: 500;
}

/* 个人中心弹窗样式 */
.profile-modal {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    z-index: 1010;
    overflow: hidden;
    /* border-radius: 10px; */
}

.profile-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    width: 90%;
    max-width: 450px;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    /* animation: fadeIn 0.3s ease; */
}

.profile-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
}

.profile-modal-header h3 {
    margin: 0;
    padding: 0;
    color: white;
    font-size: 18px;
    text-align: left;
}

.profile-modal-header h3:before,
.profile-modal-header h3:after {
    display: none;
}

.close-btn {
    cursor: pointer;
    font-size: 18px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    transition: all 0.3s;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
}

.profile-modal-body {
    padding: 20px;
    max-height: 500px;
    overflow-y: auto;
}

.user-phone-info {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px;
    background-color: #f5f9ff;
    border-radius: 6px;
    margin-bottom: 20px;
    border-left: 3px solid #34495e;
}

.user-phone-info i {
    font-size: 20px;
    color: #34495e;
}

#userPhoneDisplay {
    font-weight: 600;
    font-size: 16px;
    color: #2c3e50;
}

.package-list-title {
    font-weight: 600;
    font-size: 16px;
    margin-bottom: 15px;
    color: #2c3e50;
    padding-bottom: 10px;
    border-bottom: 1px solid #ecf0f1;
}

.package-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.package-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background-color: #f8f9fa;
    transition: all 0.3s;
    border: 1px solid #e0e0e0;
}

.package-item:hover {
    background-color: #f1f8e9;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    border-color: #c8e6c9;
}

.package-info {
    flex: 1;
}

.package-info h4 {
    margin: 0 0 5px 0;
    color: #2c3e50;
    font-size: 16px;
}

.package-duration {
    color: #7f8c8d;
    font-size: 13px;
}

.package-price {
    font-weight: 700;
    color: #e74c3c;
    font-size: 18px;
    margin-right: 15px;
}

.buy-btn {
    background: linear-gradient(to right, #43a047, #2e7d32);
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    margin: 0;
}

.buy-btn:hover {
    background: linear-gradient(to right, #388e3c, #1b5e20);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(46, 125, 50, 0.2);
}

/* 媒体查询，确保在较小屏幕上也能正常显示 */
@media (max-width: 600px) {
    .profile-modal-content {
        width: 95%;
    }
    
    .package-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .package-price {
        margin-right: 0;
    }
    
    .buy-btn {
        align-self: stretch;
        width: 100%;
    }
}

/* 套餐加载状态和错误信息样式 */
.loading-package,
.error-message,
.empty-message {
    padding: 20px;
    text-align: center;
    color: #7f8c8d;
    font-size: 14px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px dashed #e0e0e0;
}

.error-message {
    color: #e74c3c;
    background-color: #fff2f0;
    border-color: #ffccc7;
}

.empty-message {
    color: #2c3e50;
    background-color: #f5f7fa;
    border-color: #d1d8e0;
}

/* 支付二维码弹窗样式 */
.payment-modal {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1020;
    overflow: hidden;
    /* border-radius: 10px; */
}

.payment-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    width: 90%;
    max-width: 380px;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    /* animation: fadeIn 0.3s ease; */
}

.payment-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
}

.payment-modal-header h3 {
    margin: 0;
    padding: 0;
    color: white;
    font-size: 18px;
    text-align: left;
}

.payment-modal-header h3:before,
.payment-modal-header h3:after {
    display: none;
}

.payment-modal-body {
    padding: 20px;
    text-align: center;
}

.payment-info {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px dashed #e0e0e0;
}

.payment-info h4 {
    margin: 0 0 10px 0;
    font-size: 20px;
    color: #333;
}

.payment-price {
    font-size: 24px;
    font-weight: 700;
    color: #e74c3c;
}

.qrcode-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.payment-qrcode {
    width: 200px;
    height: 200px;
    background-color: #f9f9f9;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    margin: 0 auto;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.payment-qrcode img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: absolute;
    top: 0;
    left: 0;
}

.payment-tips {
    margin-top: 15px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.payment-tips p {
    margin: 5px 0;
}

/* 加载中状态样式 */
.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    width: 100%;
    text-align: center;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top: 4px solid #43a047;
    animation: spin 1.5s linear infinite;
    margin-bottom: 15px;
}

.loading-spinner p {
    color: #666;
    font-size: 14px;
    margin: 0;
}

/* 错误状态样式 */
.loading-spinner.error p {
    color: #e74c3c;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 状态指示器样式 */
.status-indicator {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    background-color: #f5f5f5;
    border-radius: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    position: absolute;
    top: 0;
    right: 0;
    z-index: 10;
    font-size: 13px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background-color: #aaa;
    margin-right: 8px;
    display: inline-block;
    position: relative;
}

.status-dot.running {
    background-color: #4CAF50;
    box-shadow: 0 0 0 4px rgba(76, 175, 80, 0.2);
    animation: pulse 1.5s infinite;
}

.status-text {
    font-size: 13px;
    font-weight: 500;
    color: #555;
}

.status-text.running {
    color: #2e7d32;
    font-weight: 600;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.4);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(76, 175, 80, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
    }
}

/* 内容输入框与删除按钮的容器样式 */
#csvList {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

#csvList .group-item {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
}

/* 删除内容按钮样式 */
.delete-content {
    cursor: pointer;
    color: #c62828;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.7;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px 10px;
    border-radius: 4px;
    font-size: 13px;
    background-color: rgba(198, 40, 40, 0.1);
}

.delete-content:hover {
    opacity: 1;
    background-color: rgba(198, 40, 40, 0.2);
    color: #b71c1c;
}

/* 禁用状态样式 */
.disabled-btn {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
    background: #cccccc !important;
    color: #666666 !important;
    box-shadow: none !important;
}

.disabled-btn:hover {
    background: #cccccc !important;
    transform: none !important;
    box-shadow: none !important;
}

.disabled-file {
    opacity: 0.6;
    cursor: not-allowed;
}

.disabled-file input[type="file"] {
    pointer-events: none;
}

input:disabled {
    background-color: #f5f5f5 !important;
    cursor: not-allowed;
    opacity: 0.8;
    color: #777;
}

/* 自定义Toast弹窗样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    width: auto;
    max-width: 80%;
}

.toast {
    display: flex;
    align-items: center;
    background-color: #333;
    color: white;
    padding: 12px 20px;
    border-radius: 8px;
    margin-bottom: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: toast-in 0.3s ease-out forwards;
    position: relative;
    overflow: hidden;
    min-width: 250px;
}

.toast.success {
    background-color: #43a047;
}

.toast.error {
    background-color: #e53935;
}

.toast.info {
    background-color: #1976d2;
}

.toast.warning {
    background-color: #ff9800;
}

.toast-icon {
    margin-right: 12px;
    font-size: 18px;
}

.toast-message {
    flex: 1;
    font-size: 14px;
    font-weight: 500;
}

.toast-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background-color: rgba(255, 255, 255, 0.7);
    width: 100%;
    animation: toast-progress 2s linear forwards;
}

@keyframes toast-in {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes toast-out {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

@keyframes toast-progress {
    from {
        width: 100%;
    }
    to {
        width: 0%;
    }
}

/* 重置按钮红色主题样式 */
#resetScrollBtn, #resetKeywordBtn {
    background: linear-gradient(to right, #e53935, #c62828);
    box-shadow: 0 3px 6px rgba(198, 40, 40, 0.2);
}

#resetScrollBtn:hover, #resetKeywordBtn:hover {
    background: linear-gradient(to right, #d32f2f, #b71c1c);
    box-shadow: 0 5px 10px rgba(198, 40, 40, 0.4);
}

#resetScrollBtn:active, #resetKeywordBtn:active {
    box-shadow: 0 2px 3px rgba(198, 40, 40, 0.2);
}

/* 禁用状态的重置按钮样式 */
#resetScrollBtn.disabled-btn, #resetKeywordBtn.disabled-btn {
    background: linear-gradient(to right, #ef9a9a, #e57373);
    cursor: not-allowed;
    box-shadow: none;
}

/* 设置按钮样式 */
.settings-btn {
    background: transparent;
    color: #fff;
    border: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s;
    padding: 0;
    margin: 0;
    box-shadow: none;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(30deg);
}

.settings-btn i {
    font-size: 16px;
    margin-right: 0;
}

/* 设置弹窗样式 */
.settings-modal {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    /* backdrop-filter: blur(3px); */
}

.settings-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    /* animation: floatIn 0.3s ease-out; */
}

.settings-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.settings-modal-header h3 {
    margin: 0;
    color: #2e7d32;
    font-size: 20px;
    text-align: left;
}

.settings-modal-header h3:before,
.settings-modal-header h3:after {
    display: none;
}

.settings-modal-body {
    padding: 10px 0;
}

.settings-group {
    margin-bottom: 20px;
}

.settings-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2e7d32;
}

.settings-group input {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    background-color: #f9f9f9;
}

.settings-group input:focus {
    border-color: #43a047;
    box-shadow: 0 0 0 2px rgba(67, 160, 71, 0.2);
    outline: none;
    background-color: #fff;
}

.settings-buttons {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    margin-top: 25px;
}

.settings-buttons button {
    padding: 10px 20px;
    min-width: 100px;
}

#resetSettingsBtn {
    background: linear-gradient(to right, #757575, #616161);
}

#resetSettingsBtn:hover {
    background: linear-gradient(to right, #616161, #424242);
}

.settings-container {
    display: flex;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s;
    height: 36px;
    box-sizing: border-box;
}

.settings-container:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 用户协议弹窗样式 */
.agreement-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.agreement-modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 80%;
    max-width: 600px;
    height: 80%;
    max-height: 600px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: modalFadeIn 0.3s ease;
}

.agreement-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
}

.agreement-modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
}

.close-agreement-btn {
    cursor: pointer;
    color: #999;
    font-size: 18px;
    transition: color 0.2s;
}

.close-agreement-btn:hover {
    color: #333;
}

.agreement-modal-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
}

.agreement-modal-body iframe {
    width: 100%;
    height: 100%;
    border: none;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 添加禁用按钮样式 */
button.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* 续费提示弹窗样式 */
.renewal-modal {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    z-index: 1020;
    overflow: hidden;
}

.renewal-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: white;
    width: 90%;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    /* animation: modalFadeIn 0.3s ease; */
}

.renewal-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
}

.renewal-modal-header h3 {
    margin: 0;
    padding: 0;
    color: white;
    font-size: 18px;
    text-align: left;
}

.renewal-modal-header h3:before,
.renewal-modal-header h3:after {
    display: none;
}

.renewal-modal-body {
    padding: 25px 20px;
    text-align: center;
}

.renewal-icon {
    font-size: 40px;
    color: #e74c3c;
    margin-bottom: 20px;
}

.renewal-message {
    margin-bottom: 25px;
}

.renewal-message p {
    margin: 5px 0;
    color: #333;
    font-size: 16px;
}

.renewal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.cancel-renewal-btn {
    background: #ecf0f1 !important;
    color: #7f8c8d;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
}

.cancel-renewal-btn:hover {
    background-color: #dde4e6 !important;
    color: #2c3e50;
}

.confirm-renewal-btn {
    background: linear-gradient(to right, #e74c3c, #c0392b);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s;
    font-weight: bold;
}

.confirm-renewal-btn:hover {
    background: linear-gradient(to right, #d44333, #b03428);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(231, 76, 60, 0.2);
}
