// 初始化查询本地数据是否存在contentInfo、keywordMap
chrome.storage.local.get("contentInfo", (result) => {
  if (result.contentInfo) {
    showCSVList(result.contentInfo);
  }
});

// 开始自动回复
document.getElementById("start").addEventListener("click", async () => {
  chrome.storage.local.get([window.STORAGE_KEYS.SEND, window.STORAGE_KEYS.REPLY], async (result) => {
    // 调用auth.js的checkAuthStatus函数
    const res = await checkAuthStatus();
    if (res.code !== 0) {
      toast.error(res.msg);
      return;
    }

    const intervalVal = parseInt(document.getElementById("interval").value);

    // 验证interval最小值为10秒
    if (intervalVal < 10) {
      toast.warning("间隔时间至少为10秒");
      return;
    }

    const interval = intervalVal * 1000;

    // 获取所有输入框内容并存入数组
    const contentInputs = document.querySelectorAll(
      "#csvList .group-item .contentInfo"
    );
    const contentInfoArray = [];
    for (let i = 0; i < contentInputs.length; i++) {
      if (contentInputs[i].value.trim() !== "") {
        contentInfoArray.push(contentInputs[i].value);
      }
    }

    // 检查是否有内容
    if (contentInfoArray.length === 0) {
      toast.error("请至少输入一条回复内容");
      return;
    }

    let selector = result[window.STORAGE_KEYS.SEND] || "button-cQ2edH";
    console.log(selector, "result");

    chrome.storage.local.set(
      {
        running: true,
        selector: selector,
        interval: interval,
        contentInfo: contentInfoArray,
        currentIndex: 0,
      },
      () => {
        // 获取所有匹配的标签页
        chrome.tabs.query({ url: "https://eos.douyin.com/*" }, (tabs) => {
          if (tabs.length > 0) {
            // 向所有匹配的标签页注入脚本
            tabs.forEach(tab => {
              chrome.scripting.executeScript({
                target: { tabId: tab.id },
                files: ["content-scripts/auto-reply.js"],
              });
            });
          }
        });
        // 更新状态指示器为运行中
        updateScrollStatus(true);
      }
    );
  });
});

function showCSVList(dataArray) {
  const trueArray = [...dataArray];
  const ul = document.getElementById("csvList");
  ul.innerHTML = ""; // 清空旧内容
  for (let i = 0; i < trueArray.length; i++) {
    // 创建group-item容器
    const groupItem = document.createElement("div");
    groupItem.className = "group-item";

    // 创建输入框
    const input = document.createElement("input");
    input.className = "contentInfo";
    input.type = "text";
    input.placeholder = "请输入内容";
    input.value = trueArray[i];

    // 创建删除按钮
    const deleteBtn = document.createElement("span");
    deleteBtn.className = "delete-content";
    deleteBtn.textContent = "删除";

    // 添加删除按钮点击事件
    deleteBtn.addEventListener("click", function () {
      groupItem.remove();
      // 检查删除后的数量
      updateDeleteButtonsVisibility();
    });

    // 将输入框和删除按钮添加到group-item
    groupItem.appendChild(input);
    groupItem.appendChild(deleteBtn);

    // 添加到csvList容器中
    ul.appendChild(groupItem);
  }

  // 如果没有数据，创建一个空的输入框
  if (trueArray.length === 0) {
    // 创建group-item容器
    const groupItem = document.createElement("div");
    groupItem.className = "group-item";

    // 创建输入框
    const input = document.createElement("input");
    input.className = "contentInfo";
    input.type = "text";
    input.placeholder = "请输入内容";

    // 创建删除按钮
    const deleteBtn = document.createElement("span");
    deleteBtn.className = "delete-content";
    deleteBtn.textContent = "删除";
    deleteBtn.style.display = "none"; // 默认隐藏删除按钮

    // 添加删除按钮点击事件
    deleteBtn.addEventListener("click", function () {
      // 不允许删除最后一个输入框
      if (ul.querySelectorAll(".group-item").length > 1) {
        groupItem.remove();
        updateDeleteButtonsVisibility();
      }
    });

    // 将输入框和删除按钮添加到group-item
    groupItem.appendChild(input);
    groupItem.appendChild(deleteBtn);

    // 添加到csvList容器中
    ul.appendChild(groupItem);
  }

  // 更新删除按钮的可见性
  updateDeleteButtonsVisibility();
}

// 更新滚动回复输入框删除按钮的可见性
function updateDeleteButtonsVisibility() {
  const csvList = document.getElementById("csvList");
  if (!csvList) return;

  const groupItems = csvList.querySelectorAll(".group-item");
  const deleteButtons = csvList.querySelectorAll(".delete-content");

  // 如果只有一个输入框，隐藏删除按钮；否则显示所有删除按钮
  if (groupItems.length <= 1) {
    deleteButtons.forEach((btn) => {
      btn.style.display = "none";
    });
  } else {
    deleteButtons.forEach((btn) => {
      btn.style.display = "";
    });
  }
}

//自动回复文件选择器
document.getElementById("autoReplayFile").addEventListener("change", (event) => {
    const file = event.target.files[0];
    const reader = new FileReader();

    reader.onload = function (e) {
      const csvData = e.target.result;
      const csvArray = [];

      // 增强版CSV解析（处理带逗号的字段）
      const rows = csvData.split(/\r?\n/);
      rows.forEach((row) => {
        if (!row.trim()) return;

        // 使用正则表达式匹配CSV字段（支持带引号内容）
        const fields = row.match(/(".*?"|[^",]+)(?=\s*,|\s*$)/g) || [];
        const decodedFields = fields.map((field) => {
          // 移除字段两侧多余引号并处理转义
          return field.replace(/^"(.*)"$/, "$1").replace(/""/g, '"');
        });

        csvArray.push(decodedFields); // 得到二维数组
      });

      // 修复原型链问题
      const safeArray = Array.from(csvArray);
      // 显示处理后的数据
      showCSVList(safeArray);
      // 存储到chrome.storage（如果需要）
      // chrome.storage.local.set({
      //     csvData: safeArray,
      //     currentIndex: 0
      // });

      // 清空文件选择器，确保下次选择相同文件时能触发change事件
      event.target.value = "";
    };

    reader.readAsText(file, "GB18030"); // 使用更全面的中文编码
  });

document.getElementById("stop").addEventListener("click", () => {
  chrome.storage.local.set({ running: false }, () => {
    // 更新状态指示器为停止运行
    updateScrollStatus(false);
  });
});

// 更新滚动回复状态指示器
function updateScrollStatus(isRunning) {
  const statusDot = document.querySelector("#scrollStatus .status-dot");
  const statusText = document.querySelector("#scrollStatus .status-text");

  if (isRunning) {
    statusDot.classList.add("running");
    statusText.classList.add("running");
    statusText.textContent = "正在运行";

    // 禁用新增和删除功能
    disableContentEditing(true);

    // 禁用开始按钮，启用停止按钮
    const startBtn = document.getElementById("start");
    const stopBtn = document.getElementById("stop");
    const resetBtn = document.getElementById("resetScrollBtn");
    if (startBtn) {
      startBtn.disabled = true;
      startBtn.classList.add("disabled-btn");
      startBtn.title = "正在运行中";
    }
    if (stopBtn) {
      stopBtn.disabled = false;
      stopBtn.classList.remove("disabled-btn");
      stopBtn.title = "";
    }
    if (resetBtn) {
      resetBtn.disabled = true;
      resetBtn.classList.add("disabled-btn");
      resetBtn.title = "停止运行后可重置";
    }
  } else {
    statusDot.classList.remove("running");
    statusText.classList.remove("running");
    statusText.textContent = "停止运行";

    // 恢复新增和删除功能
    disableContentEditing(false);

    // 启用开始按钮，禁用停止按钮
    const startBtn = document.getElementById("start");
    const stopBtn = document.getElementById("stop");
    const resetBtn = document.getElementById("resetScrollBtn");
    if (startBtn) {
      startBtn.disabled = false;
      startBtn.classList.remove("disabled-btn");
      startBtn.title = "";
    }
    if (stopBtn) {
      stopBtn.disabled = true;
      stopBtn.classList.add("disabled-btn");
      stopBtn.title = "当前未运行";
    }
    if (resetBtn) {
      resetBtn.disabled = false;
      resetBtn.classList.remove("disabled-btn");
      resetBtn.title = "";
    }
  }
}

// 页面加载时检查运行状态
document.addEventListener("DOMContentLoaded", () => {
  // 默认禁用停止按钮，因为初始状态是未运行
  const stopBtn = document.getElementById("stop");
  if (stopBtn) {
    stopBtn.disabled = true;
    stopBtn.classList.add("disabled-btn");
    stopBtn.title = "当前未运行";
  }

  // 默认禁用关键词回复的停止按钮
  const cancelQAButton = document.getElementById("cancelQAButton");
  if (cancelQAButton) {
    cancelQAButton.disabled = true;
    cancelQAButton.classList.add("disabled-btn");
    cancelQAButton.title = "当前未运行";
  }

  // 默认禁用商品讲解的停止按钮
  const productStopBtn = document.getElementById("productStop");
  if (productStopBtn) {
    productStopBtn.disabled = true;
    productStopBtn.classList.add("disabled-btn");
    productStopBtn.title = "当前未运行";
  }

  // 获取存储的运行状态并更新界面
  chrome.storage.local.get(["running", "qaRunning", "productRunning", "interval", "productPlayTime"], (result) => {
    // 更新滚动回复状态
    updateScrollStatus(result.running === true);

    // 更新关键词回复状态
    updateKeywordStatus(result.qaRunning === true);

    // 更新商品讲解状态
    if (typeof updateProductStatus === "function") {
      updateProductStatus(result.productRunning === true);
    }
    
    // 如果本地存储中有interval值，则设置到输入框
    if (result.interval) {
      // 将毫秒转换回秒
      const intervalInSeconds = result.interval / 1000;
      const intervalInput = document.getElementById("interval");
      if (intervalInput) {
        intervalInput.value = intervalInSeconds;
      }
    }
    
    // 如果本地存储中有商品讲解间隔值，则设置到输入框
    if (result.productPlayTime) {
      const productPlayTimeInput = document.getElementById("productPlayTime");
      if (productPlayTimeInput) {
        productPlayTimeInput.value = result.productPlayTime;
      }
    }
  });
});

// 更新关键词回复状态指示器
function updateKeywordStatus(isRunning) {
  const statusDot = document.querySelector("#keywordStatus .status-dot");
  const statusText = document.querySelector("#keywordStatus .status-text");

  if (isRunning) {
    statusDot.classList.add("running");
    statusText.classList.add("running");
    statusText.textContent = "正在运行";

    // 禁用新增和删除功能
    disableQAEditing(true);

    // 禁用确认按钮，启用停止按钮
    const saveQAButton = document.getElementById("saveQAButton");
    const cancelQAButton = document.getElementById("cancelQAButton");
    const resetKeywordBtn = document.getElementById("resetKeywordBtn");
    if (saveQAButton) {
      saveQAButton.disabled = true;
      saveQAButton.classList.add("disabled-btn");
      saveQAButton.title = "正在运行中";
    }
    if (cancelQAButton) {
      cancelQAButton.disabled = false;
      cancelQAButton.classList.remove("disabled-btn");
      cancelQAButton.title = "";
    }
    if (resetKeywordBtn) {
      resetKeywordBtn.disabled = true;
      resetKeywordBtn.classList.add("disabled-btn");
      resetKeywordBtn.title = "停止运行后可重置";
    }
  } else {
    statusDot.classList.remove("running");
    statusText.classList.remove("running");
    statusText.textContent = "停止运行";

    // 恢复新增和删除功能
    disableQAEditing(false);

    // 启用确认按钮，禁用停止按钮
    const saveQAButton = document.getElementById("saveQAButton");
    const cancelQAButton = document.getElementById("cancelQAButton");
    const resetKeywordBtn = document.getElementById("resetKeywordBtn");
    if (saveQAButton) {
      saveQAButton.disabled = false;
      saveQAButton.classList.remove("disabled-btn");
      saveQAButton.title = "";
    }
    if (cancelQAButton) {
      cancelQAButton.disabled = true;
      cancelQAButton.classList.add("disabled-btn");
      cancelQAButton.title = "当前未运行";
    }
    if (resetKeywordBtn) {
      resetKeywordBtn.disabled = false;
      resetKeywordBtn.classList.remove("disabled-btn");
      resetKeywordBtn.title = "";
    }
  }
}

// 禁用或启用滚动回复的编辑功能
function disableContentEditing(disabled) {
  // 禁用/启用新增按钮
  const addContentBtn = document.getElementById("addContentBtn");
  if (addContentBtn) {
    addContentBtn.disabled = disabled;
    if (disabled) {
      addContentBtn.classList.add("disabled-btn");
      addContentBtn.title = "运行中无法新增输入框";
    } else {
      addContentBtn.classList.remove("disabled-btn");
      addContentBtn.title = "";
    }
  }

  // 禁用/启用删除按钮
  const deleteButtons = document.querySelectorAll("#csvList .delete-content");
  deleteButtons.forEach((btn) => {
    if (disabled) {
      btn.style.pointerEvents = "none";
      btn.classList.add("disabled-btn");
      btn.title = "运行中无法删除";
    } else {
      btn.style.pointerEvents = "";
      btn.classList.remove("disabled-btn");
      btn.title = "";

      // 恢复时要检查可见性
      updateDeleteButtonsVisibility();
    }
  });

  // 禁用/启用输入框
  const inputs = document.querySelectorAll("#csvList .contentInfo");
  inputs.forEach((input) => {
    input.disabled = disabled;
    if (disabled) {
      input.title = "运行中无法编辑";
    } else {
      input.title = "";
    }
  });
  
  // 禁用/启用间隔时间输入框
  const intervalInput = document.getElementById("interval");
  if (intervalInput) {
    intervalInput.disabled = disabled;
    if (disabled) {
      intervalInput.title = "运行中无法修改间隔时间";
    } else {
      intervalInput.title = "";
    }
  }

  // 禁用/启用文件导入
  const fileInput = document.getElementById("autoReplayFile");
  if (fileInput) {
    fileInput.disabled = disabled;
    const fileContainer = fileInput.parentElement;
    if (fileContainer) {
      if (disabled) {
        fileContainer.classList.add("disabled-file");
        fileContainer.title = "运行中无法导入文件";
      } else {
        fileContainer.classList.remove("disabled-file");
        fileContainer.title = "";
      }
    }
  }
}

// 禁用或启用关键词回复的编辑功能
function disableQAEditing(disabled) {
  // 禁用/启用新增按钮
  const addQAButton = document.getElementById("addQAButton");
  if (addQAButton) {
    addQAButton.disabled = disabled;
    if (disabled) {
      addQAButton.classList.add("disabled-btn");
      addQAButton.title = "运行中无法新增问答对";
    } else {
      addQAButton.classList.remove("disabled-btn");
      addQAButton.title = "";
    }
  }

  // 禁用/启用删除按钮
  const deleteButtons = document.querySelectorAll(
    ".qalist:not(.template) .delete-qa"
  );
  deleteButtons.forEach((btn) => {
    if (disabled) {
      btn.style.pointerEvents = "none";
      btn.classList.add("disabled-btn");
      btn.title = "运行中无法删除";
    } else {
      btn.style.pointerEvents = "";
      btn.classList.remove("disabled-btn");
      btn.title = "";

      // 恢复时检查可见性
      if (typeof updateQADeleteButtonsVisibility === "function") {
        updateQADeleteButtonsVisibility();
      }
    }
  });

  // 禁用/启用输入框
  const inputs = document.querySelectorAll(".qalist:not(.template) input");
  inputs.forEach((input) => {
    input.disabled = disabled;
    if (disabled) {
      input.title = "运行中无法编辑";
    } else {
      input.title = "";
    }
  });

  // 禁用/启用文件导入
  const fileInput = document.getElementById("keywordReplayFile");
  if (fileInput) {
    fileInput.disabled = disabled;
    const fileContainer = fileInput.parentElement;
    if (fileContainer) {
      if (disabled) {
        fileContainer.classList.add("disabled-file");
        fileContainer.title = "运行中无法导入文件";
      } else {
        fileContainer.classList.remove("disabled-file");
        fileContainer.title = "";
      }
    }
  }
}

// 导出disableQAEditing函数以便其他脚本使用
window.disableQAEditing = disableQAEditing;

// 重置滚动回复按钮功能
document.getElementById("resetScrollBtn").addEventListener("click", () => {
  // 检查当前是否在运行中
  chrome.storage.local.get("running", (result) => {
    if (result.running) {
      toast.warning("请先停止运行后再重置");
      return;
    }

    // 清除 contentInfo 缓存
    chrome.storage.local.remove("contentInfo", () => {
      // 清空并重新初始化输入框
      const csvList = document.getElementById("csvList");
      csvList.innerHTML = "";
      // 创建一个空的输入框
      const groupItem = document.createElement("div");
      groupItem.className = "group-item";

      const input = document.createElement("input");
      input.className = "contentInfo";
      input.type = "text";
      input.placeholder = "请输入内容";

      const deleteBtn = document.createElement("span");
      deleteBtn.className = "delete-content";
      deleteBtn.textContent = "删除";
      deleteBtn.style.display = "none";

      groupItem.appendChild(input);
      groupItem.appendChild(deleteBtn);
      csvList.appendChild(groupItem);

      toast.success("滚动回复数据已重置");
    });
  });
});

// 禁用或启用商品讲解的编辑功能
function disableProductEditing(disabled) {
  // 禁用/启用商品列表中的复选框
  const checkboxes = document.querySelectorAll(".product-checkbox");
  checkboxes.forEach((checkbox) => {
    checkbox.disabled = disabled;
    if (disabled) {
      checkbox.title = "运行中无法选择商品";
    } else {
      checkbox.title = "";
    }
  });

  // 禁用/启用间隔时间输入框
  const intervalInput = document.getElementById("productPlayTime");
  if (intervalInput) {
    intervalInput.disabled = disabled;
    if (disabled) {
      intervalInput.title = "运行中无法修改间隔时间";
    } else {
      intervalInput.title = "";
    }
  }

  // 禁用/启用全选、反选、刷新按钮
  const controlButtons = document.querySelectorAll(".select-controls button");
  controlButtons.forEach((btn) => {
    btn.disabled = disabled;
    if (disabled) {
      btn.classList.add("disabled");
      btn.title = "运行中无法操作";
    } else {
      btn.classList.remove("disabled");
      btn.title = "";
    }
  });
}

// 更新商品讲解状态指示器
function updateProductStatus(isRunning) {
  const statusDot = document.querySelector("#productStatus .status-dot");
  const statusText = document.querySelector("#productStatus .status-text");

  if (isRunning) {
    statusDot.classList.add("running");
    statusText.classList.add("running");
    statusText.textContent = "正在运行";

    // 禁用商品选择和编辑功能
    disableProductEditing(true);

    // 禁用确认按钮，启用停止按钮
    const confirmBtn = document.getElementById("productConfirm");
    const stopBtn = document.getElementById("productStop");
    if (confirmBtn) {
      confirmBtn.disabled = true;
      confirmBtn.classList.add("disabled-btn");
      confirmBtn.title = "正在运行中";
    }
    if (stopBtn) {
      stopBtn.disabled = false;
      stopBtn.classList.remove("disabled-btn");
      stopBtn.title = "";
    }
  } else {
    statusDot.classList.remove("running");
    statusText.classList.remove("running");
    statusText.textContent = "停止运行";

    // 恢复商品选择和编辑功能
    disableProductEditing(false);

    // 启用确认按钮，禁用停止按钮
    const confirmBtn = document.getElementById("productConfirm");
    const stopBtn = document.getElementById("productStop");
    if (confirmBtn) {
      confirmBtn.disabled = false;
      confirmBtn.classList.remove("disabled-btn");
      confirmBtn.title = "";
    }
    if (stopBtn) {
      stopBtn.disabled = true;
      stopBtn.classList.add("disabled-btn");
      stopBtn.title = "当前未运行";
    }
  }
}

// 导出updateProductStatus函数以便其他脚本使用
window.updateProductStatus = updateProductStatus;
