(function () {
  // 监听存储变化，执行点击
  console.log(
    "此项目仅仅是一个Chrome扩展，用于自动回复直播间弹幕,使用前请确保已经获得授权,否则可能导致直播间被封号!"
  );
  console.log("此项目未进行任何逆向破解,所有功能均通过DOM元素模拟人工操作!");
  console.log("请勿将此项目用于任何非法用途,否则后果自负!");
  console.log("此项目不存储任何用户数据,所有数据均存储在本地!");

  // 检查直播是否开启
  function isLiveStreamActive() {
    const textarea = document.querySelector("#current-live-room textarea");
    return !!textarea;
  }

  // 过滤特殊字符函数
  function filterSpecialChars(text) {
    if (!text) return text;
    // 过滤掉特殊字符: ! < > & $ ' @ \' $ % # ` ^ * _
    return text.replace(/[!<>&$'@$%#`^*_]/g, "");
  }

  chrome.storage.local.get(
    [
      "running",
      "selector",
      "interval",
      "contentInfo",
      "currentIndex",
      "settingsReply",
    ],
    (config) => {
      if (!config.running) return;
      let DomName = config.settingsReply || "#current-live-room textarea";
      let textarea = document.querySelector(DomName);
      let currentIndex = config.currentIndex;

      let contentInfo = config.contentInfo;
      let length = contentInfo.length;
      // 循环执行器
      const loop = () => {
        if (currentIndex >= contentInfo.length || currentIndex === undefined)
          currentIndex = 0; // 循环模式
        // 安全插入内容并过滤特殊字符
        textarea.value = filterSpecialChars(contentInfo[currentIndex]);
        // 更新索引
        currentIndex = (currentIndex + 1) % contentInfo.length;
        chrome.storage.local.set({ currentIndex });
      };

      const clickElement = () => {
        // 检查直播是否开启
        if (!isLiveStreamActive()) {
          console.log(
            `[自动点击] ${new Date().toLocaleTimeString()} 直播未开启，跳过操作`
          );
          return;
        } else {
          textarea = document.querySelector(DomName);
        }

        // 修改内容并触发事件
        if (length === 0 || !config.contentInfo) {
          const defaultText =
            config.contentInfo || "喜欢的小伙伴的可以关注下主播哦~";
          textarea.value = filterSpecialChars(defaultText);
        } else {
          loop();
        }
        textarea.dispatchEvent(new Event("input", { bubbles: true }));
        textarea.dispatchEvent(new Event("change", { bubbles: true }));
        let element = document.querySelector(config.selector);
        if (!element && textarea) {
          console.log("未找到元素");

          let parent = textarea.parentNode.parentNode;
          // 在父节点中查找内容为"发送"的div元素
          const sendButtons = Array.from(parent.querySelectorAll("div")).filter(
            (div) => div.textContent.trim() === "发送"
          );
          // 如果找到匹配的元素，使用第一个作为element
          if (sendButtons.length > 0) {
            element = sendButtons[0];
          }
        }
        if (element) {
          element.click();
          console.log(
            `[自动点击] ${new Date().toLocaleTimeString()} 已触发点击`
          );
        }
      };

      clickElement();
      const timer = setInterval(clickElement, config.interval);

      chrome.storage.onChanged.addListener((changes) => {
        if (changes.running?.newValue === false) {
          clearInterval(timer);
        }
      });
    }
  );
})();
