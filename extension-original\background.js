// 监听扩展安装事件
chrome.runtime.onInstalled.addListener(() => {
    chrome.storage.local.set({ running: false, qaRunning: false, productRunning: false });
});

// 监听来自content script的消息
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // 处理打开弹窗的消息
    if (request.action === 'openPopup') {
        chrome.action.openPopup();
    }
    // 处理商品讲解停止的消息
    else if (request.action === 'productPlayStopped') {
        chrome.storage.local.set({ productRunning: false });
    }
});
