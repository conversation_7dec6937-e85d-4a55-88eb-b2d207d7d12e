const fs = require('fs');
const path = require('path');

// 源代码目录和混淆代码目录
const sourceDir = path.join(__dirname, 'extension-original');
const obfuscatedDir = path.join(__dirname, 'extension');

// 检查源代码目录是否存在
function checkSourceDirectory() {
  if (!fs.existsSync(sourceDir)) {
    console.error(`错误: 源代码目录不存在: ${sourceDir}`);
    
    // 如果混淆目录存在，可以复制为源代码目录
    if (fs.existsSync(obfuscatedDir)) {
      console.log(`将以混淆目录为基础创建源代码目录...`);
      copyDirectory(obfuscatedDir, sourceDir);
      console.log(`源代码目录已创建: ${sourceDir}`);
      console.log(`警告: 这个目录包含的是混淆后的代码。建议使用原始源代码替换此目录的内容。`);
    } else {
      console.error(`错误: 混淆目录也不存在: ${obfuscatedDir}`);
      console.log(`请手动创建 extension-original 目录并添加源代码。`);
      process.exit(1);
    }
  } else {
    console.log(`源代码目录已存在: ${sourceDir}`);
  }
}

// 复制目录
function copyDirectory(source, target) {
  // 创建目标目录
  if (!fs.existsSync(target)) {
    fs.mkdirSync(target, { recursive: true });
  }
  
  // 获取源目录下的所有文件和文件夹
  const files = fs.readdirSync(source, { withFileTypes: true });
  
  for (const file of files) {
    const sourcePath = path.join(source, file.name);
    const targetPath = path.join(target, file.name);
    
    if (file.isDirectory()) {
      // 递归复制子目录
      copyDirectory(sourcePath, targetPath);
    } else {
      // 复制文件
      fs.copyFileSync(sourcePath, targetPath);
    }
  }
}

// 主函数
function main() {
  console.log('准备开发环境...');
  checkSourceDirectory();
  console.log('开发环境准备完毕。将使用源代码目录: ' + sourceDir);
}

// 执行主函数
main();