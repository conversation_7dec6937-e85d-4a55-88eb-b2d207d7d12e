// utils/deviceFingerprint.js
const { execSync } = require('child_process')
const crypto = require('crypto')
const os = require('os')

function getPlatformFingerprint() {
    try {
        let rawData = ''
        switch (process.platform) {
            case 'win32':
                // 主板UUID + 磁盘序列号
                const uuid = execSync('wmic csproduct get uuid').toString().split('\n')[1].trim()
                const diskSerial = execSync('wmic diskdrive get serialnumber').toString().split('\n')[1].trim()
                rawData = `${uuid}|${diskSerial}`
                break
            case 'darwin':
                // 硬件UUID + 主板序列号
                const hwUUID = execSync("ioreg -rd1 -c IOPlatformExpertDevice | awk -F\\\" '/IOPlatformUUID/{print $(NF-1)}'").toString().trim()
                const boardSerial = execSync("system_profiler SPHardwareDataType | grep 'Serial Number'").toString().split(': ')[1].trim()
                rawData = `${hwUUID}|${boardSerial}`
                break
            case 'linux':
                // 系统ID + 磁盘UUID
                const machineId = execSync('cat /etc/machine-id').toString().trim()
                const diskUUID = execSync("lsblk -d -o uuid,serial | awk 'NR>1 {print $1}'").toString().trim()
                rawData = `${machineId}|${diskUUID}`
                break
            default:
                rawData = 'unknown'
        }

        // 添加不可变软件特征
        rawData += `|${os.userInfo().uid}|${process.env.PATH}`

        // 双哈希处理
        const firstHash = crypto.createHash('sha256').update(rawData).digest('hex')
        return crypto.createHmac('sha256', 'dynamic_salt').update(firstHash).digest('hex')
    } catch (error) {
        // 降级方案：使用主机名哈希
        return crypto.createHash('sha256').update(os.hostname()).digest('hex')
    }
}

module.exports = { getPlatformFingerprint }
