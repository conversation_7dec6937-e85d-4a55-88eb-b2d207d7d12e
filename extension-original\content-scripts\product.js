console.log("产品脚本已经加载完成！");

// 检查直播是否开启
function isLiveStreamActive() {
  const textarea = document.querySelector("#current-live-room textarea");
  return !!textarea;
}

(function () {
  // 全局变量，用于存储商品讲解的定时器ID
  let productPlayIntervalId = null;
  // 用于标记是否已经初始化过消息监听器
  let messageListenerInitialized = false;
  // 用于标记商品讲解是否已停止
  let playingStopped = true;
  // 用于存储当前正在讲解的商品ID
  let currentProductId = null;
  // 用于存储所有选中的商品数组
  let selectedProducts = [];

  function generateDynamicSelector(type) {
    return `[class*="${type}"]`;
  }

  // 封装一个通用的存储访问方法
  function getStorageData(keys) {
    return new Promise((resolve) => {
      chrome.storage.local.get(keys, (result) => {
        resolve(result);
      });
    });
  }

  async function getProductInfoList() {
    console.log('获取商品');
    
    let selector = "render-item-";
    let productArea = document.querySelector("#live-card-list");

    if (!productArea) {
      console.info("未找到产品");
      return false;
    }
    let productList = document.querySelectorAll(
      generateDynamicSelector(selector)
    );
    
    console.log('找到产品元素数量:', productList.length);

    let productData = [];
    productList.forEach((product, index) => {
      let inputKey = product.querySelector(".okee-current-live-input");
      let img = product.querySelector("img").src;
      let title = product.querySelector(
        ".okee-current-live-popper-trigger div"
      ).textContent;

      if (!inputKey.value || !img || !title) {
        return;
      }
      productData.push({
        id: inputKey.value,
        img: img,
        title: title,
      });
    });
    // 修复原型链问题
    productData = Array.from(productData);
    
    console.log('已收集产品数据数量:', productData.length);
    
    return new Promise((resolve) => {
      chrome.storage.local.set({ productData: productData }, function () {
        console.info("产品数据已保存到本地存储");
        resolve(true);
      });
    });
  }

  async function getProductList(productId) {
    // 检查直播是否开启
    if (!isLiveStreamActive()) {
      console.log(`[商品讲解] ${new Date().toLocaleTimeString()} 直播未开启，跳过操作`);
      return;
    }
    
    let selector = "render-item-";
    let explanation = "talking-btn-";
    try {
      const res = await getStorageData([
        "settingsProductList",
        "settingsExplanation",
      ]);
      selector = res.settingsProductList || "render-item-";
      explanation = res.settingsExplanation || "talking-btn-";
    } catch (error) {}
    let productArea = document.querySelector("#live-card-list");
    if (!productArea) {
      console.error("未找到父容器 #live-card-list");
      return;
    }
    let productList = document.querySelectorAll(
      generateDynamicSelector(selector)
    );
    console.log("产品列表:", productList);

    // 更新当前正在讲解的商品ID
    currentProductId = productId;
    
    let found = false;
    productList.forEach((product, index) => {
      let inputKey = product.querySelector(".okee-current-live-input");
      if (inputKey && inputKey.value === productId) {
        found = true;
        let btnElement = product.querySelector(
          generateDynamicSelector(explanation)
        );
        if (btnElement) {
          btnElement.click();
          console.log(
            `[自动点击] ${new Date().toLocaleTimeString()} 已触发点击商品ID: ${productId}`
          );
        } else {
          console.log(
            `[自动点击] ${new Date().toLocaleTimeString()} 未找到按钮, 商品ID: ${productId}`
          );
        }
      }
    });
    
    if (!found) {
      console.log(`未找到ID为 ${productId} 的商品`);
    }
    
    return productList;
  }

  // 停止商品讲解循环
  function stopProductPlay() {
    if (productPlayIntervalId) {
      console.log("停止商品讲解循环");
      clearInterval(productPlayIntervalId);
      productPlayIntervalId = null;
      playingStopped = true;
      
      // 如果有当前正在讲解的商品ID，再点击一次讲解按钮
      if (currentProductId) {
        console.log("停止讲解操作，再次点击当前商品:", currentProductId);
        getProductList(currentProductId);
      }
      
      // 通知UI更新状态
      chrome.runtime.sendMessage({ action: "productPlayStopped" });
    }
  }

  function listenerProductPlay(request, sendResponse) {
    // 先停止之前的循环
    stopProductPlay();
    
    let productArray = request.data.products;
    if (!productArray || productArray.length === 0) {
      console.error("没有选择商品，无法开始讲解循环");
      sendResponse({ success: false, error: "没有选择商品" });
      return;
    }

    console.log("开始商品讲解循环，选中商品数量:", productArray.length);
    console.log("选中的商品:", productArray);

    // 标记为已开始
    playingStopped = false;
    // 保存选中的商品数组
    selectedProducts = [...productArray];

    let selectProdIdList = productArray.map(item => item.id)
    // 存储到本地
    chrome.storage.local.set({ selectProdIdList: selectProdIdList }, function () {
      console.info("选中的产品数据ID已保存到本地存储");
    });
    
    // 每次讲解商品随机增加3-5秒
    let randomTime = Math.floor(Math.random() * 3000) + 3000;
    let intervalTime = 1000 * parseInt(request.data.intervalTime) + randomTime;
    
    // 立即讲解第一个商品
    getProductList(productArray[0].id);
    
    let currentIndex = 0;
    
    // 创建新的定时器并保存ID
    productPlayIntervalId = setInterval(() => {
      // 如果已经停止了，则不继续执行
      if (playingStopped) {
        clearInterval(productPlayIntervalId);
        productPlayIntervalId = null;
        return;
      }
      
      // 计算下一个索引
      currentIndex = (currentIndex + 1) % productArray.length;
      // 获取当前要讲解的商品
      const currentProduct = productArray[currentIndex];
      console.log(`讲解第 ${currentIndex + 1}/${productArray.length} 个商品:`, currentProduct.title);
      // 讲解商品
      getProductList(currentProduct.id);
    }, intervalTime);

    sendResponse({ success: true }); // 发送响应
  }

  // 初始化消息监听器
  function initMessageListener() {
    if (messageListenerInitialized) {
      console.log("消息监听器已初始化，跳过");
      return;
    }
    
    // 监听来自弹窗或后台的消息
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      console.log("收到来自后台的请求：", request);
      if (request.action === "playProduct") {
        console.log("收到开始播放请求");
        listenerProductPlay(request, sendResponse);
        return true; // 表示将异步发送响应
      } else if (request.action === "stopProduct") {
        console.log("收到停止播放请求");
        stopProductPlay();
        sendResponse({ success: true });
        return true;
      } else if (request.action === "getProductList") {
        console.log("收到获取商品列表请求");
        getProductInfoList().then(success => {
          sendResponse({ success: success });
        });
        return true; // 表示将异步发送响应
      }
    });
    
    messageListenerInitialized = true;
    console.log("消息监听器初始化完成");
  }

  // 页面加载后初始化
  setTimeout(() => {
    getProductInfoList();
    initMessageListener();
  }, 5000);

})(); // 立即执行函数结束
