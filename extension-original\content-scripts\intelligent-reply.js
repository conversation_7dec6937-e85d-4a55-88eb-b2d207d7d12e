// 使用IIFE避免污染全局作用域
console.log(
  "此项目仅仅是一个Chrome扩展，用于自动回复直播间弹幕,使用前请确保已经获得授权,否则可能导致直播间被封号!"
);
console.log("此项目未进行任何逆向破解,所有功能均通过DOM元素模拟人工操作!");
console.log("请勿将此项目用于任何非法用途,否则后果自负!");
console.log("此项目不存储任何用户数据,所有数据均存储在本地!");

// 检查直播是否开启
function isLiveStreamActive() {
  const textarea = document.querySelector("#current-live-room textarea");
  const isActive = !!textarea;
  // console.log(`[直播状态检查] 直播状态: ${isActive ? "已开启" : "未开启"}`);
  return isActive;
}

// 定期检查直播状态并尝试重启监听器
let liveStatusCheckInterval = null;

function startLiveStatusCheck() {
  if (liveStatusCheckInterval) {
    clearInterval(liveStatusCheckInterval);
  }
  
  // 上一次检查到的直播状态
  let lastLiveStatus = isLiveStreamActive();
  // console.log(`[直播状态监控] 初始直播状态: ${lastLiveStatus ? "已开启" : "未开启"}`);
  
  // 每10秒检查一次直播状态
  liveStatusCheckInterval = setInterval(() => {
    const currentStatus = isLiveStreamActive();
    
    // 状态发生变化时通知
    if (currentStatus !== lastLiveStatus) {
      // console.log(`[直播状态监控] 直播状态变化: ${lastLiveStatus ? "已开启" : "未开启"} -> ${currentStatus ? "已开启" : "未开启"}`);
      
      // 如果从未开启变为开启，尝试重启监听器
      if (currentStatus && !lastLiveStatus) {
        // console.log("[直播状态监控] 检测到直播已开启，尝试重启监听器");
        
        // 如果存在AutoReplier实例，尝试重启观察器
        if (window.__AUTO_REPLIER__) {
          try {
            // console.log("[直播状态监控] 尝试重新初始化智能回复系统");
            window.__AUTO_REPLIER__._reinitialize();
          } catch (error) {
            // console.error("[直播状态监控] 重新初始化失败:", error);
          }
        }
      }
      
      lastLiveStatus = currentStatus;
    }
  }, 10000); // 10秒检查一次
  
  // console.log("[直播状态监控] 直播状态监控已启动");
}

(() => {
  class AutoReplier {
    static CONFIG = {
      SELECTORS: {
        TARGET: "#current-live-room",
        TEXTAREA: "#current-live-room textarea",
        SEND_BUTTON: "button-cQ2edH",
      },
      MAX_RETRIES: 5,
      RETRY_DELAY: 1000,
    };

    #observer = null;
    #retryCount = 0;
    #keywordMap = new Map();
    #isEnabled = false;
    #liveUserName = "";

    constructor() {
      // console.log("[AutoReplier] 初始化智能回复系统");
      this.#init();
    }

    async #init() {
      try {
        // console.log("[AutoReplier] 开始初始化过程");
        this.#initStorageListener();
        await this.#loadConfig();
        this.#toggleObservation();
        // console.log("[AutoReplier] 初始化完成");
      } catch (error) {
        // console.error("[AutoReplier] 初始化过程中出错:", error);
        // 5秒后尝试重新初始化
        setTimeout(() => this.#init(), 5000);
      }
    }

    #initStorageListener() {
      try {
        // console.log("[AutoReplier] 初始化存储监听器");
        chrome.storage.onChanged.addListener((changes) => {
          try {
            // console.log("[AutoReplier] 存储变化:", changes);
            if (changes.keywordMap) {
              // console.log("[AutoReplier] 关键词映射表已更新");
              this.#updateKeywords(changes.keywordMap.newValue);
            }
            if (changes.qaRunning) {
              // console.log(`[AutoReplier] 运行状态变更: ${changes.qaRunning.newValue}`);
              this.#isEnabled = !!changes.qaRunning.newValue;
              this.#toggleObservation();
            }
          } catch (error) {
            // console.error("[AutoReplier] 处理存储变化时出错:", error);
          }
        });
      } catch (error) {
        // console.error("[AutoReplier] 初始化存储监听器时出错:", error);
      }
    }

    async #loadConfig() {
      try {
        // console.log("[AutoReplier] 加载配置");
        const { keywordMap = {}, qaRunning } = await chrome.storage.local.get([
          "keywordMap",
          "qaRunning",
        ]);
        // console.log(`[AutoReplier] 加载配置完成 - 关键词数量: ${Object.keys(keywordMap).length}, 运行状态: ${qaRunning}`);
        this.#updateKeywords(keywordMap);
        this.#isEnabled = !!qaRunning;
        return { keywordMap, qaRunning };
      } catch (error) {
        // console.error("[AutoReplier] 加载配置时出错:", error);
        return { keywordMap: {}, qaRunning: false };
      }
    }

    #updateKeywords(data) {
      try {
        if (!data || typeof data !== 'object') {
          // console.error("[AutoReplier] 无效的关键词数据:", data);
          return;
        }
        
        this.#keywordMap.clear();
        Object.entries(data).forEach(([key, value]) => {
          if (key && value) {
            this.#keywordMap.set(this.#normalizeKey(key), value);
          }
        });
        // console.log(`[AutoReplier] 关键词映射表已更新，当前包含 ${this.#keywordMap.size} 个关键词`);
      } catch (error) {
        // console.error("[AutoReplier] 更新关键词时出错:", error);
      }
    }

    #normalizeKey(key) {
      try {
        return key.trim().toLowerCase();
      } catch (error) {
        // console.error("[AutoReplier] 规范化关键词时出错:", error, "关键词:", key);
        return "";
      }
    }

    #toggleObservation() {
      try {
        // console.log(`[AutoReplier] 切换观察状态: ${this.#isEnabled ? "启用" : "禁用"}`);
        if (this.#isEnabled) {
          this.#startObserving();
        } else {
          this.#stopObserving();
        }
      } catch (error) {
        // console.error("[AutoReplier] 切换观察状态时出错:", error);
      }
    }

    #startObserving() {
      if (this.#observer) {
        // console.log("[AutoReplier] 观察器已存在，跳过创建");
        return;
      }

      // console.log("[AutoReplier] 尝试创建观察器");
      
      // 检查直播是否开启
      if (!isLiveStreamActive()) {
        // console.log("[AutoReplier] 直播未开启，将在稍后重试创建观察器");
        if (this.#retryCount < AutoReplier.CONFIG.MAX_RETRIES) {
          setTimeout(
            () => this.#startObserving(),
            AutoReplier.CONFIG.RETRY_DELAY
          );
          this.#retryCount++;
        } else {
          // console.log("[AutoReplier] 达到最大重试次数，放弃创建观察器");
        }
        return;
      }
      
      const targetNode = document.querySelector(
        AutoReplier.CONFIG.SELECTORS.TARGET
      );
      if (!targetNode) {
        // console.log(`[AutoReplier] 未找到目标节点，重试次数: ${this.#retryCount}/${AutoReplier.CONFIG.MAX_RETRIES}`);
        if (this.#retryCount < AutoReplier.CONFIG.MAX_RETRIES) {
          setTimeout(
            () => this.#startObserving(),
            AutoReplier.CONFIG.RETRY_DELAY
          );
          this.#retryCount++;
        } else {
          // console.log("[AutoReplier] 达到最大重试次数，放弃创建观察器");
        }
        return;
      }

      // console.log("[AutoReplier] 找到目标节点，创建观察器");
      this.#observer = new MutationObserver((mutations) => {
        mutations.forEach((mutation) => {
          if (mutation.type === "childList") {
            // console.log(`[AutoReplier] 检测到DOM变化，新增节点数: ${mutation.addedNodes.length}`);
            this.#processNodes(mutation.addedNodes);
          }
        });
      });

      try {
        this.#observer.observe(targetNode, {
          childList: true,
          subtree: true,
        });
        // console.log("[AutoReplier] 观察器已创建并开始监听");
        this.#retryCount = 0; // 重置重试计数
      } catch (error) {
        // console.error("[AutoReplier] 创建观察器失败:", error);
        if (this.#retryCount < AutoReplier.CONFIG.MAX_RETRIES) {
          setTimeout(
            () => this.#startObserving(),
            AutoReplier.CONFIG.RETRY_DELAY
          );
          this.#retryCount++;
        }
      }
    }

    #stopObserving() {
      if (this.#observer) {
        // console.log("[AutoReplier] 停止观察器");
        this.#observer.disconnect();
        this.#observer = null;
      }
      this.#retryCount = 0;
    }

    #processNodes(nodes) {
      // console.log(`[AutoReplier] 处理 ${nodes.length} 个新节点`);
      
      // 先检查直播是否开启
      if (!isLiveStreamActive()) {
        // console.log("[AutoReplier] 直播未开启，跳过处理节点");
        return;
      }
      
      // 检查关键词映射表是否为空
      if (this.#keywordMap.size === 0) {
        // console.log("[AutoReplier] 关键词映射表为空，尝试重新加载配置");
        this.#loadConfig().then(() => {
          // console.log(`[AutoReplier] 重新加载配置后关键词数量: ${this.#keywordMap.size}`);
        });
        return;
      }
      
      nodes.forEach((node) => {
        if (node.nodeType !== Node.ELEMENT_NODE) return;

        const dynamicSelectors = {
          content: this.#generateDynamicSelector("content"),
          username: this.#generateDynamicSelector("username"),
        };
        // console.log(`[AutoReplier] 动态选择器: content=${dynamicSelectors.content}, username=${dynamicSelectors.username}`);

        const contentElement = node.querySelector(dynamicSelectors.content);
        const usernameElement = node.querySelector(dynamicSelectors.username);
        
        if (!contentElement || !usernameElement) {
          // console.log("[AutoReplier] 未找到内容或用户名元素，跳过处理");
          return;
        }
        
        const content = contentElement.textContent?.trim();
        const username = usernameElement.textContent?.trim().split('：')[0];
        
        // console.log("[AutoReplier] 主播用户名:", this.#liveUserName);
        // console.log("[AutoReplier] 发布用户:", username);
        // console.log("[AutoReplier] 发布内容:", content);

        if (this.#liveUserName && this.#liveUserName === username) {
          // console.log("[AutoReplier] 主播用户名与当前用户名相同,不进行回复");
          return;
        } else {
          // 如果还没有获取到主播用户名，尝试获取
          if (!this.#liveUserName) {
            this.#tryGetLiveUserName();
          }
        }

        if (!content) {
          // console.log("[AutoReplier] 内容为空，跳过处理");
          return;
        }
        
        if (this.#liveUserName === username) {
          // console.log("[AutoReplier] 用户名与主播相同，跳过处理");
          return;
        }
        
        if (this.#keywordMap.size === 0) {
          // console.log("[AutoReplier] 关键词映射表为空，跳过匹配");
          return;
        }
        
        // console.log(`[AutoReplier] 开始匹配关键词，关键词数量: ${this.#keywordMap.size}`);
        
        // 使用更健壮的关键词匹配方法
        this.#matchAndReply(content, username);
      });
    }
    
    // 尝试获取主播用户名的方法
    #tryGetLiveUserName() {
      // console.log("[AutoReplier] 尝试获取主播用户名");
      const btnFeelgood = document.querySelector("#btn-feelgood");
      if (!btnFeelgood) {
        // console.log("[AutoReplier] 未找到 #btn-feelgood 元素，尝试其他方法获取主播用户名");
        
        // 备用方法1: 尝试从页面其他元素获取主播名
        try {
          const liveRoomHeader = document.querySelector("[class*='live-room-header']");
          if (liveRoomHeader) {
            const headerNameElement = liveRoomHeader.querySelector("h1, h2, [class*='name'], [class*='title']");
            if (headerNameElement) {
              const possibleName = headerNameElement.textContent.trim();
              if (possibleName && possibleName.length > 0 && possibleName.length < 20) {
                this.#liveUserName = possibleName.split(' ')[0].trim();
                // console.log("[AutoReplier] 备用方法1获取到主播用户名:", this.#liveUserName);
                return;
              }
            }
          }
          
          // 备用方法2: 尝试从URL或其他页面元素获取
          const profileElements = document.querySelectorAll("[class*='profile'], [class*='avatar'], [class*='author']");
          for (const element of profileElements) {
            if (element.textContent && element.textContent.length > 0 && element.textContent.length < 30) {
              const possibleName = element.textContent.trim().split(' ')[0];
              if (possibleName) {
                this.#liveUserName = possibleName;
                // console.log("[AutoReplier] 备用方法2获取到主播用户名:", this.#liveUserName);
                return;
              }
            }
          }
        } catch (error) {
          // console.error("[AutoReplier] 备用方法获取主播名失败:", error);
        }
        
        return;
      }
      
      let nameParent = btnFeelgood.parentNode;
      if (!nameParent) {
        // console.log("[AutoReplier] #btn-feelgood 元素没有父节点");
        return;
      }
      
      // 循环找到子元素包含img标签的元素
      try {
        const allElements = Array.from(nameParent.querySelectorAll("*"));
        let foundUserName = false;
        
        for (const element of allElements) {
          const hasImg = element.querySelector("img");
          if (hasImg) {
            const userName = element.textContent.trim().split(" ")[0].trim();
            if (userName) {
              this.#liveUserName = userName;
              // console.log("[AutoReplier] 获取到主播用户名:", this.#liveUserName);
              foundUserName = true;
              break;
            }
          }
        }
        
        if (!foundUserName) {
          // console.log("[AutoReplier] 未通过常规方法找到主播用户名");
        }
      } catch (error) {
        // console.error("[AutoReplier] 获取主播用户名时出错:", error);
      }
    }
    
    // 匹配关键词并触发回复
    #matchAndReply(content, username) {
      if (!content || !this.#keywordMap.size) return;
      
      try {
        const contentLower = content.toLowerCase();
        const sortedKeys = [...this.#keywordMap.keys()].sort((a, b) => b.length - a.length);
        
        // 检查是否有精确匹配
        for (const key of sortedKeys) {
          if (contentLower === key) {
            // console.log("[AutoReplier] 精确匹配到关键字:", key);
            this.#triggerReply(this.#keywordMap.get(key));
            return;
          }
        }
        
        // 检查是否有包含匹配
        for (const key of sortedKeys) {
          if (contentLower.includes(key)) {
            // console.log("[AutoReplier] 包含匹配到关键字:", key);
            this.#triggerReply(this.#keywordMap.get(key));
            return;
          }
        }
        
        // console.log("[AutoReplier] 未匹配到任何关键字");
      } catch (error) {
        // console.error("[AutoReplier] 关键词匹配过程中出错:", error);
      }
    }

    #generateDynamicSelector(type) {
      const base = type === "content" ? "item-content-" : "item-name-";
      return `[class*="${base}"]`;
    }

    async #triggerReply(text) {
      if (!text) {
        // console.log("[AutoReplier] 回复内容为空，跳过回复");
        return;
      }
      
      // 检查直播是否开启
      if (!isLiveStreamActive()) {
        // console.log(`[AutoReplier] ${new Date().toLocaleTimeString()} 直播未开启，跳过操作`);
        return;
      }
      
      // console.log(`[AutoReplier] 准备回复: "${text}"`);
      
      try {
        chrome.storage.local.get(
          ["settingsReply", "settingsSend"],
          async (result) => {
            try {
              let DomName =
                result.settingsReply || AutoReplier.CONFIG.SELECTORS.TEXTAREA;
              let buttonName =
                result.settingsSend || AutoReplier.CONFIG.SELECTORS.SEND_BUTTON;
                
              // console.log(`[AutoReplier] 使用选择器: textarea=${DomName}, button=${buttonName}`);
              
              try {
                // console.log("[AutoReplier] 等待元素出现");
                let [textarea, button] = await Promise.all([
                  this.#waitForElement(DomName),
                  this.#waitForElement(buttonName),
                ]);
                
                // console.log(`[AutoReplier] 元素状态: textarea=${!!textarea}, button=${!!button}`);
    
                if (!button && textarea) {
                  // console.log("[AutoReplier] 未找到按钮，尝试在父节点中查找");
                  let parent = textarea.parentNode.parentNode;
                  // 在父节点中查找内容为"发送"的div元素
                  const sendButtons = Array.from(
                    parent.querySelectorAll("div")
                  ).filter((div) => div.textContent.trim() === "发送");
                  // 如果找到匹配的元素，使用第一个作为button
                  if (sendButtons.length > 0) {
                    button = sendButtons[0];
                    // console.log("[AutoReplier] 找到发送按钮");
                  } else {
                    // console.log("[AutoReplier] 未找到发送按钮");
                  }
                }
    
                if (!textarea || !button) {
                  // console.log("[AutoReplier] 缺少必要元素，无法发送回复");
                  return;
                }
    
                // 过滤特殊字符
                const filteredText = this.#filterSpecialChars(text);
                // console.log(`[AutoReplier] 过滤后的文本: "${filteredText}"`);
                
                textarea.value = filteredText;
                textarea.dispatchEvent(new Event("input", { bubbles: true }));
                // console.log("[AutoReplier] 已设置输入框内容并触发事件");
    
                await new Promise((resolve) =>
                  setTimeout(resolve, Math.random() * 300 + 200)
                );
    
                // console.log("[AutoReplier] 点击发送按钮");
                button.click();
                // console.log("[AutoReplier] 回复已发送");
              } catch (error) {
                // console.error("[AutoReplier] 回复失败:", error);
                // 如果是因为直播未开启导致的错误，尝试重新检查直播状态
                if (isLiveStreamActive()) {
                  // console.log("[AutoReplier] 直播已开启但回复失败，可能是临时错误，将在3秒后重试");
                  setTimeout(() => this.#triggerReply(text), 3000);
                }
              }
            } catch (error) {
              // console.error("[AutoReplier] 处理存储数据时出错:", error);
            }
          }
        );
      } catch (error) {
        // console.error("[AutoReplier] 访问存储时出错:", error);
      }
    }

    #waitForElement(selector, timeout = 3000) {
      // console.log(`[AutoReplier] 等待元素: ${selector}, 超时: ${timeout}ms`);
      return new Promise((resolve) => {
        const start = Date.now();
        const check = () => {
          const el = document.querySelector(selector);
          if (el) {
            // console.log(`[AutoReplier] 找到元素: ${selector}`);
            return resolve(el);
          }
          if (Date.now() - start > timeout) {
            // console.log(`[AutoReplier] 等待元素超时: ${selector}`);
            return resolve(null);
          }
          requestAnimationFrame(check);
        };

        check();
      });
    }

    // 过滤特殊字符
    #filterSpecialChars(text) {
      if (!text) return text;
      // 过滤掉特殊字符: ! < > & $ ' @ \' $ % # ` ^ * _
      return text.replace(/[!<>&$'@$%#`^*_]/g, '');
    }

    // 提供一个公共方法用于外部重新初始化
    _reinitialize() {
      try {
        // console.log("[AutoReplier] 开始重新初始化");
        this.#stopObserving();
        this.#retryCount = 0;
        this.#toggleObservation();
        // console.log("[AutoReplier] 重新初始化完成");
      } catch (error) {
        // console.error("[AutoReplier] 重新初始化过程中出错:", error);
        // 3秒后尝试再次重新初始化
        setTimeout(() => this._reinitialize(), 3000);
      }
    }
  }

  // 单例模式初始化
  if (!window.__AUTO_REPLIER__) {
    window.__AUTO_REPLIER__ = new AutoReplier();
    
    // 启动直播状态监控
    startLiveStatusCheck();
  } else {
    // console.log("[AutoReplier] 智能回复系统已存在，跳过初始化");
  }
})();
