/**
 * 登录模块
 * 手机号+验证码登录/注册，如果用户不存在则自动注册
 */

document.addEventListener("DOMContentLoaded", () => {
  const loginContainer = document.getElementById("login-container");
  const tabContainer = document.querySelector(".tab-container");
  const phoneInput = document.getElementById("phone");
  const verifyCodeInput = document.getElementById("verifyCode");
  const sendCodeBtn = document.getElementById("sendCodeBtn");
  const loginBtn = document.getElementById("loginBtn");
  const expireDateElement = document.getElementById("expireDate");
  const profileBtn = document.getElementById("profileBtn");
  const profileModal = document.getElementById("profileModal");
  const closeProfileModal = document.getElementById("closeProfileModal");
  const userPhoneDisplay = document.getElementById("userPhoneDisplay");
  const paymentModal = document.getElementById("paymentModal");
  const closePaymentModal = document.getElementById("closePaymentModal");
  const packageNameElement = document.getElementById("packageName");
  const packagePriceElement = document.getElementById("packagePrice");
  const renewalModal = document.getElementById("renewalModal");
  const closeRenewalModal = document.getElementById("closeRenewalModal");
  const cancelRenewalBtn = document.getElementById("cancelRenewalBtn");
  const confirmRenewalBtn = document.getElementById("confirmRenewalBtn");

  // 获取auth.js中定义的baseUrl
  const apiBaseUrl = window.baseUrl || "";

  // 初始化时检查用户登录状态和token有效性
  if (typeof checkAuthStatus === "function") {
    checkAuthStatus();
  }

  // 输入验证
  phoneInput.addEventListener("input", validatePhone);
  verifyCodeInput.addEventListener("input", validateCode);

  // 发送验证码按钮事件
  sendCodeBtn.addEventListener("click", sendVerificationCode);

  // 登录按钮事件
  loginBtn.addEventListener("click", handleLogin);

  // 个人中心按钮事件
  if (profileBtn) {
    profileBtn.addEventListener("click", openProfileCenter);
  }

  // 关闭个人中心弹窗事件
  if (closeProfileModal) {
    closeProfileModal.addEventListener("click", () => {
      profileModal.style.display = "none";
    });
  }

  // 关闭支付弹窗事件
  if (closePaymentModal) {
    closePaymentModal.addEventListener("click", () => {
      handleQrcodeModal(false);
    });
  }

  // 关闭续费提示弹窗事件
  if (closeRenewalModal) {
    closeRenewalModal.addEventListener("click", () => {
      hideRenewalModal();
    });
  }

  // 取消续费按钮事件
  if (cancelRenewalBtn) {
    cancelRenewalBtn.addEventListener("click", () => {
      hideRenewalModal();
    });
  }

  // 确认续费按钮事件
  if (confirmRenewalBtn) {
    confirmRenewalBtn.addEventListener("click", () => {
      hideRenewalModal();
      openProfileCenter();
    });
  }

  // 点击弹窗外部关闭
  window.addEventListener("click", (event) => {
    if (event.target === profileModal) {
      profileModal.style.display = "none";
    }
    if (event.target === paymentModal) {
      handleQrcodeModal(false);
    }
    if (event.target === renewalModal) {
      hideRenewalModal();
    }
  });

  /**
   * 验证手机号格式
   */
  function validatePhone() {
    const phone = phoneInput.value.trim();
    const isValid = /^1[3-9]\d{9}$/.test(phone);

    if (isValid) {
      phoneInput.classList.remove("invalid");
      phoneInput.classList.add("valid");
      phoneInput.parentElement.classList.remove("error");
      phoneInput.parentElement.classList.add("success");
    } else {
      phoneInput.classList.remove("valid");
      phoneInput.classList.add("invalid");
      phoneInput.parentElement.classList.remove("success");
      phoneInput.parentElement.classList.add("error");
    }

    updateLoginButtonState();
    return isValid;
  }

  /**
   * 验证验证码格式
   */
  function validateCode() {
    const code = verifyCodeInput.value.trim();
    const isValid = /^\d{6}$/.test(code);

    if (isValid) {
      verifyCodeInput.classList.remove("invalid");
      verifyCodeInput.classList.add("valid");
      verifyCodeInput.parentElement.classList.remove("error");
      verifyCodeInput.parentElement.classList.add("success");
    } else {
      verifyCodeInput.classList.remove("valid");
      verifyCodeInput.classList.add("invalid");
      verifyCodeInput.parentElement.classList.remove("success");
      verifyCodeInput.parentElement.classList.add("error");
    }

    updateLoginButtonState();
    return isValid;
  }

  /**
   * 更新登录按钮状态
   */
  function updateLoginButtonState() {
    const phoneValid = /^1[3-9]\d{9}$/.test(phoneInput.value.trim());
    const codeValid = /^\d{6}$/.test(verifyCodeInput.value.trim());

    loginBtn.disabled = !(phoneValid && codeValid);
  }

  /**
   * 发送验证码
   */
  async function sendVerificationCode() {

    const agreement = document.getElementById("agreement").checked;
    if (!agreement) {
      toast.error("请先同意用户协议");
      return;
    }

    if (!validatePhone()) {
      toast.error("请输入正确的手机号");
      return;
    }

    const phone = phoneInput.value.trim();

    // 禁用发送按钮并开始倒计时
    let countdown = 60;
    sendCodeBtn.disabled = true;
    sendCodeBtn.textContent = `${countdown}秒`;

    const timer = setInterval(() => {
      countdown--;
      sendCodeBtn.textContent = `${countdown}秒`;

      if (countdown <= 0) {
        clearInterval(timer);
        sendCodeBtn.disabled = false;
        sendCodeBtn.textContent = "获取验证码";
      }
    }, 1000);

    try {
      // 使用http模块发送验证码请求
      const data = await http.post("/index/common/login/code", {
        tell: phone,
      });
      console.log("验证码发送成功");
      toast.success("验证码发送成功");
    } catch (error) {
      console.error("发送验证码出错:", error);
      toast.error(`验证码发送失败: ${error.message}`);
      clearInterval(timer);
      sendCodeBtn.disabled = false;
      sendCodeBtn.textContent = "获取验证码";
    }
  }

  /**
   * 打开个人中心
   */
  async function openProfileCenter() {
    // 调用auth.js的checkAuthStatus函数
    // const res = await checkAuthStatus();
    // if (res.code !== 0) {
    //   toast.error(res.msg);
    //   return;
    // }
    // 显示个人中心弹窗
    if (profileModal) {
      // 获取用户手机号并设置脱敏显示
      // 使用统一的getUserData方法（适配浏览器插件环境）
      const { userPhone } = await getUserData(["userPhone"]);
      if (userPhone) {
        displayUserInfo(userPhone);
      }

      // 加载套餐列表
      await loadPackageList();

      // 显示弹窗
      profileModal.style.display = "block";
    }
  }

  window.openProfileCenter = openProfileCenter;

  /**
   * 显示用户信息
   */
  function displayUserInfo(phone) {
    if (userPhoneDisplay && phone) {
      // 手机号脱敏处理：138****1234
      const maskedPhone = phone.substring(0, 3) + "****" + phone.substring(7);
      userPhoneDisplay.textContent = maskedPhone;
    }
  }

  /**
   * 加载套餐列表
   */
  async function loadPackageList() {
    const packageList = document.querySelector(".package-list");
    if (!packageList) return;

    // 显示加载中
    packageList.innerHTML =
      '<div class="loading-package">正在加载套餐信息...</div>';

    try {
      // 使用http模块获取套餐信息
      const result = await http.get("/index/common/member", {
        cache: "no-store",
      });

      renderPackageList(result.data);
    } catch (error) {
      console.error("加载套餐出错:", error);
      packageList.innerHTML =
        '<div class="error-message">加载套餐信息失败，请重试</div>';
    }
  }

  /**
   * 渲染套餐列表
   */
  function renderPackageList(packages) {
    console.log(packages);
    const packageList = document.querySelector(".package-list");
    if (!packageList) return;

    if (!packages || packages.length === 0) {
      packageList.innerHTML = '<div class="empty-message">暂无可用套餐</div>';
      return;
    }

    let html = "";
    packages.forEach((pkg) => {
      html += `
                <div class="package-item" data-id="${pkg.id}">
                    <div class="package-info">
                        <h4>${pkg.title}</h4>
                        <div class="package-duration">${pkg.validity_days}天</div>
                    </div>
                    <div class="package-price">¥${pkg.price}</div>
                    <button class="buy-btn" data-id="${pkg.id}">购买</button>
                </div>
            `;
    });

    packageList.innerHTML = html;

    // 添加购买按钮事件
    document.querySelectorAll(".buy-btn").forEach((btn) => {
      btn.addEventListener("click", function () {
        const packageId = this.getAttribute("data-id");
        handleBuyPackage(packageId);
      });
    });
  }

  /**
   * 处理购买套餐
   */
  async function handleBuyPackage(packageId) {
    // 找到套餐详情
    const packageItems = document.querySelectorAll(".package-item");
    let packageName = "";
    let packagePrice = "";

    packageItems.forEach((item) => {
      if (item.getAttribute("data-id") === packageId) {
        packageName = item.querySelector("h4").textContent;
        packagePrice = item.querySelector(".package-price").textContent;
      }
    });

    // 设置支付弹窗信息
    if (packageNameElement) {
      packageNameElement.textContent = packageName;
    }

    if (packagePriceElement) {
      packagePriceElement.textContent = packagePrice;
    }

    // 首先显示弹窗和加载中的状态
    if (paymentModal) {
      // 显示加载状态
      const qrcodeContainer = document.getElementById("paymentQrcode");
      if (qrcodeContainer) {
        qrcodeContainer.innerHTML = `
                    <div class="loading-spinner">
                        <div class="spinner"></div>
                        <p>正在加载支付二维码...</p>
                    </div>
                `;
      }

      // 显示支付弹窗
      handleQrcodeModal(true);

      try {
        // 获取用户Token
        const { userToken } = await getUserData(["userToken"]);

        // 从服务器获取支付二维码
        const data = await http.post("/index/user/order/unified", {
          package_id: Number(packageId),
        });

        console.log("二维码", data);
        // 订单号
        const orderId = data.data.outTradeNo;
        // 二维码内容
        const codeUrl = data.data.codeUrl;
        console.log("订单号", orderId);

        // 设置二维码图片
        if (qrcodeContainer) {
          qrcodeContainer.innerHTML = ""; // 清空容器
          new QRCode(qrcodeContainer, {
            text: codeUrl,
            width: 200,
            height: 200,
            colorDark: "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H,
          });
          // 轮训请求订单状态
          checkOrderStatus(orderId);
        }
      } catch (error) {
        console.error("获取支付二维码出错:", error);
        toast.error(`获取支付信息失败: ${error.message}`);
        // 显示错误状态
        if (qrcodeContainer) {
          qrcodeContainer.innerHTML = `
                        <div class="loading-spinner error">
                            <i class="fas fa-exclamation-circle" style="font-size: 40px; color: #e74c3c; margin-bottom: 15px;"></i>
                            <p>获取二维码失败，请重试</p>
                        </div>
                    `;
        }
      }
    }
  }

  // 校验订单状态 轮训请求订单有效期15分钟，3000毫秒请求一次
  // 订单状态 1: 未支付 2: 已支付 3: 退款
  function checkOrderStatus(orderId) {
    // 轮训请求订单有效期15分钟，3000毫秒请求一次
    const interval = 3000;
    const maxTime = 15 * 60 * 1000;

    // 成功的话关闭二维码页面，并提示用户
    const checkOrderStatus = async () => {
      try {
        const data = await http.post("/index/user/order/status", {
          order_number: orderId,
        });

        // 支付成功
        if (data.code === 0 && data.data.status === 2) {
          // 关闭二维码页面，并提示用户
          handleQrcodeModal(false);
          toast.success("支付成功");
          if (data.data.end_time) {
            const expireDateElement = document.getElementById("expireDate");
            expireDateElement.textContent = `到期时间: ${data.data.end_time}`;
            saveUserData({
              expireDate: data.data.end_time,
            });
          }
        }
      } catch (error) {
        console.error("检查订单状态出错:", error);
      }
    };

    checkOrderStatus();
    // 全局变量
    window.timer = setInterval(checkOrderStatus, interval);
    setTimeout(() => {
      if (window.timer) {
        clearInterval(window.timer);
      }
    }, maxTime);
  }

  // 统一处理二维码弹窗打开和关闭操作
  function handleQrcodeModal(open = true) {
    if (open) {
      paymentModal.style.display = "block";
    } else {
      paymentModal.style.display = "none";
      console.log("取消请求");
      if (window.timer) {
        clearInterval(window.timer);
      }
    }
  }

  /**
   * 处理登录/注册
   */
  async function handleLogin() {
    const device_fingerprint = localStorage.getItem("device_fingerprint")?.replaceAll('"', '');
    console.log("设备指纹", device_fingerprint);
    const agreement = document.getElementById("agreement").checked;
    if (!agreement) {
      toast.error("请先同意用户协议");
      return;
    }

    if (!validatePhone() || !validateCode()) {
      toast.error("请输入正确的手机号和验证码");
      return;
    }

    const phone = phoneInput.value.trim();
    const code = verifyCodeInput.value.trim();

    // 禁用登录按钮
    loginBtn.disabled = true;
    loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 登录中...';

    try {
      // 使用http模块发送登录请求
      const data = await http.post("/index/common/login/tell", {
        tell: phone,
        code,
        machine_code: device_fingerprint,
      });

      // console.log("登录结果", data);
      // 详细输出数据结构，帮助诊断
      // console.log("登录返回详细数据:", JSON.stringify(data.data));
      // console.log(data.data.exp);

      // 登录成功，保存token
      const userData = {
        userToken: data.data.token,
        userPhone: phone,
        expireDate:
          data.data.expire_date ||
          data.data.expireDate ||
          data.data.exp ||
          "未设置", // 尝试所有可能的到期时间字段
      };

      // 使用auth.js中的函数
      if (typeof saveUserData === "function") {
        saveUserData(userData);
      }

      // 显示功能界面
      if (typeof showMainPage === "function") {
        showMainPage();

        clearInterval(window.AuthTimer);
        window.AuthTimer = null;
        // 轮询请求数据
        window.AuthTimer = setInterval(() => {
          window.checkAuthStatus();
        }, 1000 * 30);
      } else {
        loginContainer.style.display = "none";
        tabContainer.style.display = "flex";
      }

      // 设置到期时间显示
      if (expireDateElement) {
        expireDateElement.textContent = `到期时间: ${userData.expireDate}`;
      }

      // 清空输入框
      phoneInput.value = "";
      verifyCodeInput.value = "";
    } catch (error) {
      console.error("登录出错:", error);
      toast.error(`登录失败: ${error.message}`);
    } finally {
      // 恢复登录按钮
      loginBtn.disabled = false;
      loginBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> 登录/注册';
    }
  }

  // 添加用户协议点击事件
  document
    .getElementById("showAgreement")
    .addEventListener("click", function (e) {
      e.preventDefault();
      // 创建协议弹窗
      const agreementModal = document.createElement("div");
      agreementModal.className = "agreement-modal";
      agreementModal.innerHTML = `
      <div class="agreement-modal-content">
        <div class="agreement-modal-header">
          <h3>用户协议</h3>
          <span class="close-agreement-btn"><i class="fas fa-times"></i></span>
        </div>
        <div class="agreement-modal-body">
          <iframe src="http://live.jiajs.cn/agreement.html" width="100%" height="100%" frameborder="0"></iframe>
        </div>
      </div>
    `;

      // 添加到页面
      document.body.appendChild(agreementModal);

      // 添加关闭按钮事件
      agreementModal
        .querySelector(".close-agreement-btn")
        .addEventListener("click", function () {
          document.body.removeChild(agreementModal);
        });

      // 点击弹窗外部区域关闭
      agreementModal.addEventListener("click", function (event) {
        if (event.target === agreementModal) {
          document.body.removeChild(agreementModal);
        }
      });
    });

  /**
   * 显示续费提示弹窗
   */
  function showRenewalModal() {
    if (renewalModal) {
      renewalModal.style.display = "block";
    }
  }

  /**
   * 隐藏续费提示弹窗
   */
  function hideRenewalModal() {
    if (renewalModal) {
      renewalModal.style.display = "none";
    }
  }

  // 将showRenewalModal设为全局函数以便auth.js调用
  window.showRenewalModal = showRenewalModal;
});
