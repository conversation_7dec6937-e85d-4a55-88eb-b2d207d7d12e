/**
 * 设置功能模块
 * 用于管理四个输入框的设置和缓存
 */

document.addEventListener('DOMContentLoaded', () => {
    // DOM 元素
    const settingsBtn = document.getElementById('settingsBtn');
    const settingsModal = document.getElementById('settingsModal');
    const closeSettingsModal = document.getElementById('closeSettingsModal');
    const saveSettingsBtn = document.getElementById('saveSettingsBtn');
    const resetSettingsBtn = document.getElementById('resetSettingsBtn');
    
    // 设置输入框
    const replyText = document.getElementById('replyText');
    const sendText = document.getElementById('sendText');
    const productListText = document.getElementById('productListText');
    const explanationText = document.getElementById('explanationText');
    
    // 使用auth.js导出的全局STORAGE_KEYS常量
    const STORAGE_KEYS = window.STORAGE_KEYS;
    
    // 打开设置弹窗
    settingsBtn.addEventListener('click', openSettingsModal);
    
    // 关闭设置弹窗
    closeSettingsModal.addEventListener('click', closeModal);
    
    // 保存设置
    saveSettingsBtn.addEventListener('click', saveSettings);
    
    // 重置设置
    resetSettingsBtn.addEventListener('click', resetSettings);
    
    /**
     * 打开设置弹窗
     */
    function openSettingsModal() {
        // 加载缓存的设置数据
        loadSettings();
        // 显示弹窗
        settingsModal.style.display = 'block';
    }
    
    /**
     * 关闭弹窗
     */
    function closeModal() {
        settingsModal.style.display = 'none';
    }
    
    /**
     * 加载缓存的设置
     */
    function loadSettings() {
        chrome.storage.local.get([
            STORAGE_KEYS.REPLY,
            STORAGE_KEYS.SEND,
            STORAGE_KEYS.PRODUCT_LIST,
            STORAGE_KEYS.EXPLANATION
        ], (result) => {
            // 如果有缓存数据，则填充到输入框
            if (result[STORAGE_KEYS.REPLY]) {
                replyText.value = result[STORAGE_KEYS.REPLY];
            }
            
            if (result[STORAGE_KEYS.SEND]) {
                sendText.value = result[STORAGE_KEYS.SEND];
            }
            
            if (result[STORAGE_KEYS.PRODUCT_LIST]) {
                productListText.value = result[STORAGE_KEYS.PRODUCT_LIST];
            }
            
            if (result[STORAGE_KEYS.EXPLANATION]) {
                explanationText.value = result[STORAGE_KEYS.EXPLANATION];
            }
        });
    }
    
    /**
     * 保存设置到缓存
     */
    function saveSettings() {
        const settings = {
            [STORAGE_KEYS.REPLY]: replyText.value.trim(),
            [STORAGE_KEYS.SEND]: sendText.value.trim(),
            [STORAGE_KEYS.PRODUCT_LIST]: productListText.value.trim(),
            [STORAGE_KEYS.EXPLANATION]: explanationText.value.trim()
        };
        
        chrome.storage.local.set(settings, () => {
            // 显示保存成功提示
            if (typeof toast !== 'undefined' && toast.success) {
                toast.success('设置已保存');
            } else {
                alert('设置已保存');
            }
            
            // 关闭弹窗
            closeModal();
        });
    }
    
    /**
     * 重置设置缓存
     */
    function resetSettings() {
        // 确认是否重置
        if (confirm('确定要重置所有设置吗？')) {
            chrome.storage.local.remove([
                STORAGE_KEYS.REPLY,
                STORAGE_KEYS.SEND,
                STORAGE_KEYS.PRODUCT_LIST,
                STORAGE_KEYS.EXPLANATION
            ], () => {
                // 清空输入框
                replyText.value = '';
                sendText.value = '';
                productListText.value = '';
                explanationText.value = '';
                
                // 显示重置成功提示
                if (typeof toast !== 'undefined' && toast.info) {
                    toast.info('设置已重置');
                } else {
                    alert('设置已重置');
                }
            });
        }
    }
    
    // 点击弹窗外部关闭弹窗
    window.addEventListener('click', (event) => {
        if (event.target === settingsModal) {
            closeModal();
        }
    });
}); 