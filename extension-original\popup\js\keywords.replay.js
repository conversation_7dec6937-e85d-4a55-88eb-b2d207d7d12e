// 初始化查询本地数据是否存在keywordMap
chrome.storage.local.get("keywordMap", (result) => {
  if (result.keywordMap) {
    renderQAInputs(result.keywordMap);
  }
});

//关键词回复文件选择器
document
  .getElementById("keywordReplayFile")
  .addEventListener("change", (event) => {
    const file = event.target.files[0];
    const reader = new FileReader();
    reader.onload = function (e) {
      const csvData = e.target.result;
      responseMap = {}; // 清空旧数据
      // 增强CSV解析：处理带逗号、换行的字段
      const rows = csvData.split(/\r?\n/).filter((row) => row.trim() !== "");
      rows.forEach((row, index) => {
        // 跳过标题行（假设第一行是标题）
        if (index === 0 && /(关键字|回复)/.test(row)) return;

        // 正则分割：只分割不在引号内的逗号
        const columns = row.split(/,(?=(?:(?:[^"]*"){2})*[^"]*$)/);

        if (columns.length < 2) {
          console.warn(`第${index + 1}行数据不完整: ${row}`);
          return;
        }

        // 清理字段：去除首尾空格和引号
        const keyword = columns[0].trim().replace(/^"|"$/g, "");
        const reply = columns[1].trim().replace(/^"|"$/g, "");

        // 处理重复关键词
        if (responseMap.hasOwnProperty(keyword)) {
          console.warn(`关键词重复: ${keyword}，将覆盖旧值`);
        }

        responseMap[keyword] = reply;
      });

      //解析完成，映射表已更新
      renderQAInputs(responseMap);
      // document.getElementById('keywordReplayFile').value = ''; // 清空文件选择器的值，以便可以重新选择文件
      // 清空文件选择器，确保下次选择相同文件时能触发change事件
      event.target.value = "";
    };
    reader.readAsText(file, "GB18030");
  });

/**
*
将关键词回复数据渲染到输入框列表
* @param {Object} data - 关键词回复映射表 {关键词: 回复}
*/
function renderQAInputs(data) {
  const qaContainer = document.querySelector(".qa-box");
  const addButton = document.getElementById("addQAButton");

  // 清空现有行：删除所有非模板行
  const existingQAs = qaContainer.querySelectorAll(".qalist:not(.template)");
  existingQAs.forEach((qa) => qa.remove());

  // 动态生成新行（始终从隐藏模板克隆）
  const template = document.querySelector(".qalist.template"); // 获取隐藏模板
  Object.entries(data).forEach(([keyword, reply]) => {
    const qaDiv = createQARow(template, keyword, reply);
    qaContainer.insertBefore(qaDiv, addButton);
  });

  toggleEmptyTip(data);
}

/**
*
创建单个关键词回复输入行
* @param {Element} template -
模板元素
* @param {string} keyword -
关键词
* @param {string} reply -
回复内容
*/
function createQARow(template, keyword, reply) {
  const newQa = template.cloneNode(true);
  // 关键修复：移除模板标识类
  newQa.classList.remove("template");
  newQa.style.display = "flex"; // 确保显示

  // 填充数据
  const inputs = newQa.querySelectorAll("input");
  inputs[0].value = keyword;
  inputs[1].value = reply;

  // 绑定删除事件
  newQa.querySelector(".delete-qa").addEventListener("click", () => {
    newQa.remove();
    toggleEmptyTip(getCurrentData());
  });

  return newQa;
}

/**
 * 切换空数据提示
 * @param {Object} data - 当前数据
 */
function toggleEmptyTip(data) {
  const tip = document.querySelector(".empty-tip");
  const isEmpty = Object.keys(data).length === 0;

  if (isEmpty && !tip) {
    const newTip = document.createElement("div");
    newTip.className = "empty-tip";
    newTip.textContent = "暂无数据，请导入CSV文件";
    document.querySelector(".box").appendChild(newTip);
  } else if (!isEmpty && tip) {
    tip.remove();
  }
}

// ----------------------
// 模块: 数据存取
// ----------------------

/**
 * 保存数据到Chrome存储
 * @param {Object} data - 关键词回复数据
 * @param {boolean} running  是否正在运行
 */
function saveToStorage(data, running) {
  chrome.storage.local.set(
    {
      keywordMap: data,
      qaRunning: running,
    },
    () => {
      console.log("数据已持久化");
      // 确保状态指示器与实际状态一致
      updateKeywordStatus(running);
    }
  );
}

/**
 * 从当前界面收集数据
 * @returns {Object} 当前所有关键词回复数据
 */
function getCurrentData() {
  const qaList = document.querySelectorAll(".qalist:not(.template)");
  return Array.from(qaList).reduce((map, qa) => {
    const inputs = qa.querySelectorAll("input");
    if (inputs[0].value) {
      map[inputs[0].value] = inputs[1].value;
    }
    return map;
  }, {});
}

/**
 * 从界面收集当前所有QA数据
 * @returns {Object} 关键词回复映射表
 */
function collectQAData() {
  const qaList = document.querySelectorAll(".qalist:not(.template)");
  return Array.from(qaList).reduce((map, qa) => {
    const inputs = qa.querySelectorAll("input");
    const keyword = inputs[0].value.trim();
    const reply = inputs[1].value.trim();

    // 过滤空关键词
    if (keyword) {
      if (map[keyword]) {
        console.warn(`发现重复关键词: ${keyword}，将覆盖旧值`);
      }
      map[keyword] = reply;
    }
    return map;
  }, {});
}

/**
 * 保存数据到Storage
 */
async function handleSave() {
  try {
    const currentData = collectQAData();

    // 数据验证
    if (Object.keys(currentData).length === 0) {
      throw new Error("没有可保存的有效数据");
    }

    // 持久化存储
    await saveToStorage(currentData, true);

    // 可选：重新渲染确保一致性
    renderQAInputs(currentData);
  } catch (error) {
    console.error("保存失败:", error);
    alert(`保存失败: ${error.message}`);
  }
}

// 绑定保存按钮
document.getElementById("saveQAButton").addEventListener("click", async () => {
  chrome.storage.local.get([window.STORAGE_KEYS.REPLY],
    async () => {
      // 调用auth.js的checkAuthStatus函数
      const res = await checkAuthStatus();
      if (res.code !== 0) {
        toast.error(res.msg);
        return;
      }
      try {
        const currentData = collectQAData();

        // 数据验证
        if (Object.keys(currentData).length === 0) {
          throw new Error("没有可保存的有效数据");
        }

        // 更新状态为运行中
        if (typeof updateKeywordStatus === "function") {
          updateKeywordStatus(true);
        } else {
          // 如果updateKeywordStatus未定义，可能需要从popup.js导入
          console.error("updateKeywordStatus函数未找到");
        }

        // 持久化存储
        chrome.storage.local.set(
          {
            keywordMap: currentData,
            qaRunning: true,
          },
          () => {
            console.log("数据已持久化");

            // 执行内容脚本
            chrome.tabs.query({ active: true, currentWindow: true }, (tabs) => {
              chrome.scripting.executeScript({
                target: { tabId: tabs[0].id },
                files: ["content-scripts/intelligent-reply.js"],
              });
            });
          }
        );
      } catch (error) {
        console.error("保存失败:", error);
        alert(`保存失败: ${error.message}`);
      }
    }
  );
});

//取消运行
document.getElementById("cancelQAButton").addEventListener("click", () => {
  chrome.storage.local.set({ qaRunning: false }, () => {
    console.log("取消运行");
    // 更新状态指示器为停止运行
    if (typeof updateKeywordStatus === "function") {
      updateKeywordStatus(false);
    } else {
      console.error("updateKeywordStatus函数未找到");
    }
  });
});

// 从popup.js中引用的函数
function updateKeywordStatus(isRunning) {
  const statusDot = document.querySelector("#keywordStatus .status-dot");
  const statusText = document.querySelector("#keywordStatus .status-text");

  if (isRunning) {
    statusDot.classList.add("running");
    statusText.classList.add("running");
    statusText.textContent = "正在运行";

    // 禁用编辑功能
    if (typeof disableQAEditing === "function") {
      disableQAEditing(true);
    } else if (window.disableQAEditing) {
      window.disableQAEditing(true);
    } else {
      console.warn("disableQAEditing函数未找到，输入框可能未被禁用");
      // 尝试手动禁用
      disableQAInputs(true);
    }

    // 禁用确认按钮，启用停止按钮
    const saveQAButton = document.getElementById("saveQAButton");
    const cancelQAButton = document.getElementById("cancelQAButton");
    if (saveQAButton) {
      saveQAButton.disabled = true;
      saveQAButton.classList.add("disabled-btn");
      saveQAButton.title = "正在运行中";
    }
    if (cancelQAButton) {
      cancelQAButton.disabled = false;
      cancelQAButton.classList.remove("disabled-btn");
      cancelQAButton.title = "";
    }
  } else {
    statusDot.classList.remove("running");
    statusText.classList.remove("running");
    statusText.textContent = "停止运行";

    // 恢复编辑功能
    if (typeof disableQAEditing === "function") {
      disableQAEditing(false);
    } else if (window.disableQAEditing) {
      window.disableQAEditing(false);
    } else {
      console.warn("disableQAEditing函数未找到，输入框可能未被启用");
      // 尝试手动启用
      disableQAInputs(false);
    }

    // 启用确认按钮，禁用停止按钮
    const saveQAButton = document.getElementById("saveQAButton");
    const cancelQAButton = document.getElementById("cancelQAButton");
    if (saveQAButton) {
      saveQAButton.disabled = false;
      saveQAButton.classList.remove("disabled-btn");
      saveQAButton.title = "";
    }
    if (cancelQAButton) {
      cancelQAButton.disabled = true;
      cancelQAButton.classList.add("disabled-btn");
      cancelQAButton.title = "当前未运行";
    }
  }
}

// 备用禁用函数，在找不到disableQAEditing的情况下使用
function disableQAInputs(disabled) {
  // 禁用/启用新增按钮
  const addQAButton = document.getElementById("addQAButton");
  if (addQAButton) {
    addQAButton.disabled = disabled;
    if (disabled) {
      addQAButton.classList.add("disabled-btn");
      addQAButton.title = "运行中无法新增问答对";
    } else {
      addQAButton.classList.remove("disabled-btn");
      addQAButton.title = "";
    }
  }

  // 禁用/启用删除按钮
  const deleteButtons = document.querySelectorAll(
    ".qalist:not(.template) .delete-qa"
  );
  deleteButtons.forEach((btn) => {
    if (disabled) {
      btn.style.pointerEvents = "none";
      btn.classList.add("disabled-btn");
      btn.title = "运行中无法删除";
    } else {
      btn.style.pointerEvents = "";
      btn.classList.remove("disabled-btn");
      btn.title = "";
    }
  });

  // 禁用/启用输入框
  const inputs = document.querySelectorAll(".qalist:not(.template) input");
  inputs.forEach((input) => {
    input.disabled = disabled;
    if (disabled) {
      input.title = "运行中无法编辑";
    } else {
      input.title = "";
    }
  });

  // 禁用/启用文件导入
  const fileInput = document.getElementById("keywordReplayFile");
  if (fileInput) {
    fileInput.disabled = disabled;
    const fileContainer = fileInput.parentElement;
    if (fileContainer) {
      if (disabled) {
        fileContainer.classList.add("disabled-file");
        fileContainer.title = "运行中无法导入文件";
      } else {
        fileContainer.classList.remove("disabled-file");
        fileContainer.title = "";
      }
    }
  }
}

// 重置关键词回复按钮功能
document.getElementById("resetKeywordBtn").addEventListener("click", () => {
  // 检查当前是否在运行中
  chrome.storage.local.get("qaRunning", (result) => {
    if (result.qaRunning) {
      toast.warning("请先停止运行后再重置");
      return;
    }

    // 清除 keywordMap 缓存
    chrome.storage.local.remove("keywordMap", () => {
      // 清空并重置为默认三个空输入框
      const qaBox = document.querySelector(".qa-box");
      const existingQAs = qaBox.querySelectorAll(".qalist:not(.template)");
      existingQAs.forEach((qa) => qa.remove());

      const template = document.querySelector(".qalist.template");
      // 创建三个默认空白行
      for (let i = 0; i < 3; i++) {
        const qaDiv = template.cloneNode(true);
        qaDiv.classList.remove("template");
        qaDiv.style.display = "flex";

        // 清空可能的值
        const inputs = qaDiv.querySelectorAll("input");
        inputs.forEach((input) => (input.value = ""));

        // 绑定删除事件
        qaDiv.querySelector(".delete-qa").addEventListener("click", () => {
          qaDiv.remove();
        });

        // 插入到添加按钮前
        const addButton = document.getElementById("addQAButton");
        qaBox.insertBefore(qaDiv, addButton);
      }

      toast.success("关键词回复数据已重置");
    });
  });
});
