const fs = require('fs');
const path = require('path');
const JavaScriptObfuscator = require('javascript-obfuscator');
const glob = require('glob');

// 混淆配置
const obfuscationOptions = {
  compact: true,
  controlFlowFlattening: true,
  controlFlowFlatteningThreshold: 0.7,
  deadCodeInjection: true,
  deadCodeInjectionThreshold: 0.4,
  debugProtection: false,
  debugProtectionInterval: 0,
  disableConsoleOutput: true,
  identifierNamesGenerator: 'hexadecimal',
  log: false,
  numbersToExpressions: true,
  renameGlobals: false,
  selfDefending: true,
  simplify: true,
  splitStrings: true,
  splitStringsChunkLength: 10,
  stringArray: true,
  stringArrayCallsTransform: true,
  stringArrayEncoding: ['base64'],
  stringArrayIndexShift: true,
  stringArrayRotate: true,
  stringArrayShuffle: true,
  stringArrayWrappersCount: 2,
  stringArrayWrappersChainedCalls: true,
  stringArrayWrappersParametersMaxCount: 4,
  stringArrayWrappersType: 'function',
  stringArrayThreshold: 0.75,
  transformObjectKeys: true,
  unicodeEscapeSequence: false
};

// 源代码目录和输出目录
const sourceDir = path.join(__dirname, 'extension-original');
const outputDir = path.join(__dirname, 'extension');

// 创建输出目录结构
function createDirectoryStructure(targetDir) {
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
}

// 混淆单个文件
function obfuscateFile(filePath, outputPath) {
  try {
    console.log(`混淆文件: ${filePath}`);
    const fileContent = fs.readFileSync(filePath, 'utf8');
    
    // 对JS文件进行混淆
    if (path.extname(filePath) === '.js') {
      const obfuscatedCode = JavaScriptObfuscator.obfuscate(
        fileContent,
        obfuscationOptions
      ).getObfuscatedCode();
      
      fs.writeFileSync(outputPath, obfuscatedCode);
      console.log(`  ✅ 混淆成功: ${outputPath}`);
    } 
    // 非JS文件直接复制
    else {
      fs.copyFileSync(filePath, outputPath);
      console.log(`  📋 复制文件: ${outputPath}`);
    }
  } catch (error) {
    console.error(`  ❌ 混淆失败: ${filePath}`, error);
  }
}

// 处理整个目录
function processDirectory(sourceDir, targetDir) {
  // 创建目标目录
  createDirectoryStructure(targetDir);
  
  // 获取源目录下的所有文件和文件夹
  const files = fs.readdirSync(sourceDir, { withFileTypes: true });
  
  for (const file of files) {
    const sourcePath = path.join(sourceDir, file.name);
    const targetPath = path.join(targetDir, file.name);
    
    if (file.isDirectory()) {
      // 递归处理子目录
      processDirectory(sourcePath, targetPath);
    } else {
      // 处理文件
      obfuscateFile(sourcePath, targetPath);
    }
  }
}

// 清空输出目录
function cleanOutputDirectory() {
  if (fs.existsSync(outputDir)) {
    fs.rmSync(outputDir, { recursive: true, force: true });
  }
  fs.mkdirSync(outputDir, { recursive: true });
  console.log(`已清空输出目录: ${outputDir}`);
}

// 检查源代码目录
function checkSourceDirectory() {
  if (!fs.existsSync(sourceDir)) {
    console.error(`错误: 源代码目录不存在: ${sourceDir}`);
    console.log('请确保 extension-original 目录存在且包含源代码。');
    process.exit(1);
  }
}

// 主函数
function main() {
  console.log('开始混淆扩展程序...');
  console.log(`源代码目录: ${sourceDir}`);
  console.log(`输出目录: ${outputDir}`);
  
  // 检查源代码目录
  checkSourceDirectory();
  
  // 清空输出目录
  cleanOutputDirectory();
  
  // 处理整个扩展目录
  processDirectory(sourceDir, outputDir);
  
  console.log('混淆完成！');
  console.log(`原始源代码目录: ${sourceDir}`);
  console.log(`混淆后的扩展程序位于: ${outputDir}`);
}

// 执行主函数
main();
