# 简单直播

这是一个基于Electron的简单直播客户端，集成了直播助手插件功能。

## 功能特点

- 加载抖音EOS网站
- 集成直播助手插件
- 提供多种方式打开插件窗口：
  - 菜单栏中的"插件 > 打开插件窗口"
  - 快捷键 `Ctrl+Shift+P`
  - 页面右下角的浮动按钮

## 开发环境配置

### 安装依赖

```bash
npm install
```

### 运行开发版本

```bash
npm run dev
```

## 应用打包

### Windows 平台打包

```bash
npm run build-win
```

打包后的应用将在 `dist` 目录下生成。

### macOS 平台打包

```bash
npm run build-mac
```

打包后的应用将在 `dist` 目录下生成。

## 插件开发

插件代码位于 `extension-original` 目录，包含以下文件：

- `manifest.json` - 插件清单文件
- `background.js` - 插件背景脚本
- `content-scripts/` - 内容脚本目录
- `popup/` - 插件弹窗目录

## 注意事项

- 打包时会自动包含 `extension` 目录
- 应用只允许访问 `https://eos.douyin.com/` 域名
- 为了安全，应用禁止了导航到其他网站

## 许可证

UNLICENSED 