const {
  app,
  BrowserWindow,
  Menu,
  dialog,
  ipcMain,
  globalShortcut,
  Notification
} = require("electron");
const path = require("path");
const fs = require("fs");
const { getPlatformFingerprint } = require('./utils/deviceFingerprint');
// 在文件顶部添加导入
const { autoUpdater } = require("electron-updater");
const log = require("electron-log");

// 确保只有一个实例在运行
const gotTheLock = app.requestSingleInstanceLock();

// 如果无法获取锁，说明已经有另一个实例在运行
if (!gotTheLock) {
  console.log('应用已经在运行，退出当前实例');
  app.quit();
} else {
  // 监听第二个实例的启动
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 如果用户尝试打开另一个应用实例，应该聚焦到已有的窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) {
        mainWindow.restore();
      }
      mainWindow.focus();

      // 可选：显示提示
      dialog.showMessageBox(mainWindow, {
        type: 'info',
        title: '程序已在运行',
        message: '简单直播已经在运行中',
        buttons: ['确定']
      });
    }
  });
}

// 读取package.json获取应用版本号
const packageJson = require('./package.json');
const appVersion = packageJson.version;

// 配置日志记录
log.transports.file.level = "debug";
autoUpdater.logger = log;

// 标记是否是手动检查更新
let isManualUpdateCheck = false;
// 添加一个标记来追踪是否已经显示了更新对话框
let updateDialogDisplayed = false;

// 添加更新检查和处理函数
function checkForUpdates(manual = false) {
  // 设置手动检查标记
  isManualUpdateCheck = manual;
  // 重置对话框显示标记
  updateDialogDisplayed = false;

  // 检查更新前发送状态给渲染进程
  mainWindow.webContents.send('update-status', { status: 'checking' });

  // 设置更新服务器地址（替换为您实际的更新服务器地址）
  autoUpdater.setFeedURL({
    provider: 'generic',
    url: 'http://live.jiajs.cn/updates/'
  });

  // 检查更新
  autoUpdater.checkForUpdates();
}

// 更新事件监听
autoUpdater.on('checking-for-update', () => {
  log.info('检查更新中...');
  mainWindow.webContents.send('update-status', { status: 'checking' });
});

autoUpdater.on('update-available', (info) => {
  log.info('有可用更新', info);
  mainWindow.webContents.send('update-status', {
    status: 'available',
    version: info.version,
    releaseNotes: info.releaseNotes
  });

  // 如果已经显示了对话框，则不再显示
  if (updateDialogDisplayed) {
    return;
  }

  // 设置标记，表示已经显示了对话框
  updateDialogDisplayed = true;

  // 准备更新内容
  let updateDetails = `发现新版本: ${info.version}\n\n`;

  // 添加更新说明
  if (info.releaseNotes) {
    // releaseNotes可能是HTML格式或字符串，这里简单处理
    if (typeof info.releaseNotes === 'string') {
      updateDetails += `更新内容:\n${info.releaseNotes}`;
    } else if (Array.isArray(info.releaseNotes)) {
      // 如果是数组格式，合并所有内容
      updateDetails += `更新内容:\n${info.releaseNotes.join('\n')}`;
    } else {
      // 尝试从HTML中提取文本
      const notesText = info.releaseNotes.replace(/<[^>]*>/g, '');
      updateDetails += `更新内容:\n${notesText}`;
    }
  } else {
    updateDetails += "暂无详细更新说明";
  }

  // 显示更新确认对话框
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: '发现新版本',
    message: `发现新版本: ${info.version}`,
    detail: updateDetails,
    buttons: ['现在更新', '稍后再说'],
    cancelId: 1
  }).then(({ response }) => {
    if (response === 0) {
      // 用户选择现在更新，开始下载
      // 重置对话框显示标记，以便下载完成后可以显示重启对话框
      updateDialogDisplayed = false;
      autoUpdater.downloadUpdate();
    }
  });
});

autoUpdater.on('update-not-available', () => {
  log.info('已是最新版本');
  mainWindow.webContents.send('update-status', { status: 'not-available' });

  // 只在手动检查更新时显示已是最新版本的提示
  if (isManualUpdateCheck) {
    dialog.showMessageBox(mainWindow, {
      type: 'info',
      title: '已是最新版本',
      message: '您当前使用的已是最新版本',
      buttons: ['确定']
    });
    // 重置手动检查标记
    isManualUpdateCheck = false;
  }
});

autoUpdater.on('download-progress', (progressObj) => {
  log.info('下载进度', progressObj);
  mainWindow.webContents.send('update-status', {
    status: 'downloading',
    progress: progressObj.percent
  });
});

autoUpdater.on('update-downloaded', (info) => {
  log.info('更新已下载', info);
  log.info('更新信息详情:', JSON.stringify({
    version: info.version,
    releaseDate: info.releaseDate,
    updateDialogDisplayed: updateDialogDisplayed
  }));

  mainWindow.webContents.send('update-status', {
    status: 'downloaded',
    version: info.version
  });

  // 如果已经显示了对话框，则不再显示
  if (updateDialogDisplayed) {
    log.info('更新对话框已显示，跳过显示重启对话框');
    return;
  }

  // 设置标记，表示已经显示了对话框
  updateDialogDisplayed = true;
  log.info('显示重启对话框');

  // 准备更新内容
  let updateDetails = `版本 ${info.version} 已下载完成，需要重启应用以安装更新。\n\n`;

  // 添加更新说明
  if (info.releaseNotes) {
    // releaseNotes可能是HTML格式或字符串，这里简单处理
    if (typeof info.releaseNotes === 'string') {
      updateDetails += `更新内容:\n${info.releaseNotes}`;
    } else if (Array.isArray(info.releaseNotes)) {
      // 如果是数组格式，合并所有内容
      updateDetails += `更新内容:\n${info.releaseNotes.join('\n')}`;
    } else {
      // 尝试从HTML中提取文本
      const notesText = info.releaseNotes.replace(/<[^>]*>/g, '');
      updateDetails += `更新内容:\n${notesText}`;
    }
  }

  // 显示安装更新对话框
  dialog.showMessageBox(mainWindow, {
    type: 'info',
    title: '更新已就绪',
    message: '更新已下载完成，需要重启应用以安装更新。',
    detail: updateDetails,
    buttons: ['立即重启', '稍后再说'],
    cancelId: 1
  }).then(({ response }) => {
    if (response === 0) {
      // 用户选择立即重启，先显示系统通知，再安装更新
      const updateNotification = new Notification({
        title: '正在更新',
        body: '软件正在更新中，更新完成会自动重启，请稍候...',
        silent: false,
        timeoutType: 'never' // 设置通知不会自动消失
      });
      
      // 显示通知
      updateNotification.show();
      
      // 延迟一小段时间以确保通知显示出来
      setTimeout(() => {
        autoUpdater.quitAndInstall(true, true);
      }, 500);
    }
  });
});

autoUpdater.on('error', (error) => {
  log.error('更新出错', error);
  mainWindow.webContents.send('update-status', {
    status: 'error',
    error: error.message
  });

  // 添加错误对话框以通知用户
  dialog.showMessageBox(mainWindow, {
    type: 'error',
    title: '更新出错',
    message: '下载更新过程中出现错误',
    detail: `错误信息: ${error.message}\n\n如果问题持续存在，请联系支持团队或手动下载最新版本。`,
    buttons: ['确定']
  });

  // 重置对话框显示标记，以便下次可以重新尝试
  updateDialogDisplayed = false;
});

function createSplash() {
  // 创建无边框启动页窗口
  splashWindow = new BrowserWindow({
    width: 400,
    height: 400,
    transparent: true, // 透明背景
    frame: false,      // 无边框
    alwaysOnTop: true, // 置顶显示
    webPreferences: {
      nodeIntegration: true
    }
  });

  // 加载启动页 HTML
  splashWindow.loadFile('splash.html');
  splashWindow.on('closed', () => (splashWindow = null));
}


// 禁用代理设置，防止ERR_PROXY_CONNECTION_FAILED错误
app.commandLine.appendSwitch("no-proxy-server");

// 在开发环境中启用热重载功能
if (process.env.NODE_ENV === "development" || !app.isPackaged) {
  try {
    require("electron-reload")(__dirname, {
      // 不使用require.resolve查找electron可执行文件
      // electron: require.resolve('electron'),
      hardResetMethod: "exit",
      // 监视这些文件的变化
      watched: [
        path.join(__dirname, "**/*.js"),
        path.join(__dirname, "**/*.html"),
        path.join(__dirname, "**/*.css"),
        path.join(__dirname, "**/*.json"),
        path.join(__dirname, "extension-original/**/*")
      ],
    });
    console.log("Hot reload enabled");
  } catch (err) {
    console.error("Failed to enable hot reload:", err);
  }
}

// 允许的URL
const allowedURL = "https://eos.douyin.com/";

// 获取应用程序的路径
const getAppPath = () => {
  // 在开发环境中使用原始源代码目录
  if (process.env.NODE_ENV === "development" || !app.isPackaged) {
    return path.join(__dirname, "extension-original");
  }

  // 在生产环境中 (应用已打包)使用混淆后的代码
  return path.join(process.resourcesPath, "extension");
};

// 插件路径
const extensionPath = getAppPath();

// 存储主窗口的引用
let mainWindow = null;

// 存储插件窗口的引用
let pluginWindow = null;

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    show: false, // 先不显示窗口
    title: "简单直播",
    icon: path.join(__dirname, "logo.ico"),
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true,
      preload: path.join(__dirname, "preload.js"),
    },
  });

  // 监听主窗口加载事件
  mainWindow.once('did-start-loading', () => {
    if (splashWindow) splashWindow.webContents.send('progress', 10); // 开始加载
  });

  mainWindow.once('did-finish-load', () => {
    if (splashWindow) splashWindow.webContents.send('progress', 90); // 页面加载完成
  });

  // 窗口准备好后最大化并显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.maximize();
    if (splashWindow) splashWindow.close();
    mainWindow.show();
  });

  // 禁用代理，直接连接
  mainWindow.webContents.session.setProxy({ mode: "direct" });

  // 加载抖音EOS网站
  mainWindow.loadURL(allowedURL);

  // 添加Ctrl+鼠标滚轮缩放功能
  // 初始缩放级别
  let currentZoomFactor = 1.0;

  // 注册IPC事件处理器，用于接收来自渲染进程的缩放请求
  ipcMain.on('zoom-in', () => {
    // 放大页面，增加缩放因子
    currentZoomFactor = Math.min(currentZoomFactor + 0.1, 3.0);
    mainWindow.webContents.setZoomFactor(currentZoomFactor);
    console.log(`页面放大，缩放级别: ${currentZoomFactor}`);
  });

  ipcMain.on('zoom-out', () => {
    // 缩小页面，减少缩放因子
    currentZoomFactor = Math.max(currentZoomFactor - 0.1, 0.5);
    mainWindow.webContents.setZoomFactor(currentZoomFactor);
    console.log(`页面缩小，缩放级别: ${currentZoomFactor}`);
  });

  // 监听键盘事件，跟踪Ctrl键状态
  global.isCtrlKeyPressed = false;

  // 监听键盘按下事件
  mainWindow.webContents.on('before-input-event', (event, input) => {
    if (input.key === 'Control') {
      if (input.type === 'keyDown') {
        global.isCtrlKeyPressed = true;
      } else if (input.type === 'keyUp') {
        global.isCtrlKeyPressed = false;
      }
    }
  });

  // 处理新窗口打开请求
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    // 只允许打开抖音EOS域名下的URL
    if (url.startsWith(allowedURL)) {
      return { action: "allow" };
    }
    // 禁止打开其他网站
    return { action: "deny" };
  });

  // 拦截所有导航，只允许访问指定URL
  mainWindow.webContents.on("will-navigate", (event, navigationUrl) => {
    if (!navigationUrl.startsWith(allowedURL)) {
      event.preventDefault();
      mainWindow.loadURL(allowedURL);
    }
  });

  // 阻止跳转到其他网站
  mainWindow.webContents.on("will-redirect", (event, navigationUrl) => {
    if (!navigationUrl.startsWith(allowedURL)) {
      event.preventDefault();
    }
  });

  // 创建菜单
  const menu = Menu.buildFromTemplate([
    {
      label: "刷新",
      role: "reload",
    },
    // {
    //   label: '插件',
    //   submenu: [
    //     {
    //       label: '加载直播助手插件',
    //       click: () => {
    //         loadExtension(mainWindow);
    //       }
    //     },
    //     {
    //       label: '打开插件窗口',
    //       click: () => {
    //         openExtensionByName(mainWindow);
    //       }
    //     },
    //     {
    //       label: '查看已加载插件',
    //       click: async () => {
    //         const extensions = mainWindow.webContents.session.getAllExtensions();
    //         let extensionInfo = extensions.map(ext =>
    //           `名称: ${ext.name}\nID: ${ext.id}\n路径: ${ext.path}`
    //         ).join('\n\n');

    //         if (extensions.length === 0) {
    //           extensionInfo = '未加载任何插件';
    //         }

    //         dialog.showMessageBox(mainWindow, {
    //           title: '已加载插件',
    //           message: '已加载的插件列表',
    //           detail: extensionInfo,
    //           buttons: ['确定']
    //         });
    //       }
    //     }
    //   ]
    // },
    // {
    //   label: "开发者工具",
    //   click: () => {
    //     mainWindow.webContents.openDevTools();
    //   },
    // },
    {
      label: "关于",
      click: () => {
        dialog.showMessageBox(mainWindow, {
          title: "关于",
          message: "简单直播",
          detail: `版本: ${appVersion}\n集成了直播助手插件的客户端\n已获得直播间运营方的合法授权\n遵守抖音平台《直播行为规范》及相关法律法规\n不用于发送垃圾信息、虚假宣传等违规行为`,
          buttons: ["确定"],
        });
      },
    },
    {
      label: "检查更新",
      click: () => {
        if (app.isPackaged) {
          checkForUpdates(true); // 传入true表示手动检查
        } else {
          dialog.showMessageBox(mainWindow, {
            title: "开发模式",
            message: "在开发模式下无法检查更新",
            buttons: ["确定"]
          });
        }
      }
    },
  ]);

  // 设置菜单
  Menu.setApplicationMenu(menu);

  // 在窗口关闭时清除引用
  mainWindow.on("closed", () => {
    mainWindow = null;
  });
}

// 静默加载插件（不显示对话框）
async function silentLoadExtension(window) {
  try {
    // 确保插件目录存在
    if (!fs.existsSync(extensionPath)) {
      console.error(`Extension directory not found: ${extensionPath}`);
      return;
    }

    // 检查session是否已设置
    const session = window.webContents.session;

    // 获取已加载的插件
    const loadedExtensions = session.getAllExtensions();

    // 如果插件已加载，则不重复加载
    const existingExtension = loadedExtensions.find(
      (ext) => ext.path === extensionPath
    );
    if (existingExtension) {
      console.log(`Extension already loaded, ID: ${existingExtension.id}`);
      return;
    }

    // 加载插件
    const extension = await session.loadExtension(extensionPath);
    console.log(
      `Extension loaded successfully: ${extensionPath}, ID: ${extension.id}`
    );
  } catch (error) {
    console.error("Failed to load extension silently:", error);
  }
}

// 加载插件
async function loadExtension(window) {
  try {
    // 确保插件目录存在
    if (!fs.existsSync(extensionPath)) {
      dialog.showErrorBox("错误", `插件目录不存在: ${extensionPath}`);
      return;
    }

    // 检查session是否已设置
    const session = window.webContents.session;

    // 获取已加载的插件
    const loadedExtensions = session.getAllExtensions();
    const extensionId = path.basename(extensionPath);

    // 如果插件已加载，则不重复加载，直接打开插件窗口
    const existingExtension = loadedExtensions.find(
      (ext) => ext.path === extensionPath
    );
    if (existingExtension) {
      dialog
        .showMessageBox(window, {
          title: "插件已加载",
          message: "直播助手插件已加载",
          detail: `插件ID: ${existingExtension.id}\n已激活`,
          buttons: ["确定", "打开插件"],
        })
        .then((result) => {
          if (result.response === 1) {
            // 打开插件窗口
            openExtensionPopup(existingExtension.id);
          }
        });
      return;
    }

    // 加载插件
    const extension = await session.loadExtension(extensionPath);
    console.log(`插件已加载: ${extensionPath}，ID: ${extension.id}`);

    dialog
      .showMessageBox(window, {
        title: "插件已加载",
        message: "直播助手插件已成功加载",
        detail: `插件ID: ${extension.id}`,
        buttons: ["确定", "刷新并打开插件"],
        defaultId: 1,
      })
      .then((result) => {
        if (result.response === 1) {
          // 用户选择了刷新页面并打开插件
          window.reload();

          // 重新加载后延迟打开插件窗口
          setTimeout(() => {
            openExtensionPopup(extension.id);
          }, 2000); // 延迟2秒等待页面加载完成
        }
      });
  } catch (error) {
    console.error("加载插件失败:", error);
    dialog.showErrorBox("加载插件失败", error.message);
  }
}

// 新增：打开插件弹出窗口
function openExtensionPopup(extensionId) {
  if (!mainWindow) return;

  // 检查是否已经有一个插件窗口打开
  if (pluginWindow && !pluginWindow.isDestroyed()) {
    // 如果已经有窗口，则聚焦它
    if (pluginWindow.isMinimized()) {
      pluginWindow.restore();
      // 等待一小段时间确保恢复完成
      setTimeout(() => {
        pluginWindow.focus();
      }, 100);
    } else {
      // 确保窗口可见
      if (!pluginWindow.isVisible()) {
        pluginWindow.show();
      }
      pluginWindow.focus();
    }
    return;
  }

  // 创建插件窗口
  pluginWindow = new BrowserWindow({
    width: 716,
    height: 740,
    parent: mainWindow,
    modal: false,
    show: true,
    icon: path.join(__dirname, "logo.ico"),
    autoHideMenuBar: false, // 不使用自动隐藏菜单栏
    webPreferences: {
      contextIsolation: false,
      nodeIntegration: false,
      preload: path.join(__dirname, 'plugin-preload.js')
    },
  });

  // 完全移除菜单栏
  pluginWindow.removeMenu();

  // 加载插件的popup页面
  const popupPath = `chrome-extension://${extensionId}/popup/popup.html`;
  pluginWindow.loadURL(popupPath);

  // 打开开发者工具查看问题
  // pluginWindow.webContents.openDevTools();

  // 窗口标题
  pluginWindow.setTitle("直播助手插件");

  // 监听窗口关闭事件
  pluginWindow.on("closed", () => {
    // 窗口关闭时，清空引用
    pluginWindow = null;
  });
}

// 根据插件名称查找并打开插件窗口
function openExtensionByName(window, silent = false) {
  if (!window) return;

  const session = window.webContents.session;
  const extensions = session.getAllExtensions();

  // 寻找扩展名包含"直播助手"或匹配插件目录的扩展
  const targetExtension = extensions.find(
    (ext) =>
      ext.name.includes("直播助手") ||
      ext.name.includes("简单直播助手") ||
      ext.path === extensionPath
  );

  if (targetExtension) {
    // 找到了匹配的扩展，打开它的弹窗
    openExtensionPopup(targetExtension.id);
    return true;
  } else if (extensions.length > 0 && !silent) {
    // 如果没有找到匹配的扩展但有其他扩展，显示选择对话框
    // 仅在非静默模式下显示对话框
    const buttons = extensions.map((ext) => ext.name);
    buttons.push("取消");

    dialog
      .showMessageBox(window, {
        title: "选择插件",
        message: "请选择要打开的插件",
        buttons: buttons,
        cancelId: buttons.length - 1,
      })
      .then((result) => {
        if (result.response < extensions.length) {
          // 用户选择了一个插件
          openExtensionPopup(extensions[result.response].id);
        }
      });
    return true;
  } else if (!silent) {
    // 没有加载任何扩展，但不是静默模式，显示对话框
    dialog
      .showMessageBox(window, {
        type: "info",
        title: "未加载插件",
        message: "没有加载任何插件",
        detail: "请先加载插件",
        buttons: ["确定", "加载插件"],
        defaultId: 0,
      })
      .then((result) => {
        if (result.response === 1) {
          // 用户选择了加载插件
          loadExtension(window);
        }
      });
    return false;
  } else {
    // 静默模式下，没有找到扩展，但不显示对话框
    return false;
  }
}

// 当Electron完成初始化并准备创建浏览器窗口时调用此方法
app.whenReady().then(async () => {
  console.log("App starting...");
  console.log(`Environment: ${app.isPackaged ? "Production" : "Development"}`);
  console.log(`Extension path: ${extensionPath}`);
  createSplash();      // 先显示启动页
  createWindow();
  if (!fs.existsSync(extensionPath)) {
    console.error(`Warning: Extension directory not found - ${extensionPath}`);
    fs.mkdirSync(extensionPath, { recursive: true });
  }

  // 注册IPC处理器
  ipcMain.handle("load-extension", () => {
    if (mainWindow) {
      loadExtension(mainWindow);
    }
  });

  // 添加获取设备指纹的IPC处理器
  ipcMain.handle("get-device-fingerprint", () => {
    return getPlatformFingerprint();
  });

  ipcMain.handle("get-extensions", () => {
    if (mainWindow) {
      return mainWindow.webContents.session.getAllExtensions();
    }
    return [];
  });

  // 检查插件窗口状态
  ipcMain.handle("check-plugin-window", () => {
    return {
      exists: !!pluginWindow && !pluginWindow.isDestroyed(),
      isVisible:
        !!pluginWindow &&
        !pluginWindow.isDestroyed() &&
        pluginWindow.isVisible(),
    };
  });

  // 聚焦插件窗口
  ipcMain.handle("focus-plugin-window", () => {
    return new Promise((resolve) => {
      if (pluginWindow && !pluginWindow.isDestroyed()) {
        // 如果窗口被最小化，先恢复它
        if (pluginWindow.isMinimized()) {
          pluginWindow.restore();

          // 添加延时，确保窗口完全恢复后再聚焦
          setTimeout(() => {
            // 确保窗口可见
            if (!pluginWindow.isVisible()) {
              pluginWindow.show();
            }

            // 聚焦窗口
            pluginWindow.focus();

            // 带上窗口信息返回成功状态
            resolve({
              success: true,
              windowId: pluginWindow.id,
              minimized: false,
              visible: true,
            });
          }, 100);
        } else {
          // 确保窗口可见
          if (!pluginWindow.isVisible()) {
            pluginWindow.show();
          }

          // 聚焦窗口
          pluginWindow.focus();

          // 返回成功状态
          resolve({
            success: true,
            windowId: pluginWindow.id,
            minimized: false,
            visible: true,
          });
        }
      } else {
        resolve({ success: false, error: "插件窗口不存在或已关闭" });
      }
    });
  });

  // 处理打开插件弹窗的请求
  ipcMain.handle("open-extension-popup", async () => {
    if (!mainWindow) {
      return { success: false, error: "主窗口不存在" };
    }

    try {
      // 获取已加载的扩展
      const session = mainWindow.webContents.session;
      const extensions = session.getAllExtensions();

      // 检查是否有扩展
      if (extensions.length === 0) {
        // 尝试自动加载插件
        await silentLoadExtension(mainWindow);
        // 重新获取扩展列表
        const updatedExtensions = session.getAllExtensions();
        if (updatedExtensions.length === 0) {
          return { success: false, error: "未加载任何插件" };
        }
      }

      // 打开插件窗口（使用静默模式）
      const opened = openExtensionByName(mainWindow, true);
      if (!opened) {
        return { success: false, error: "未找到匹配的插件" };
      }
      return { success: true };
    } catch (error) {
      console.error("打开插件窗口失败:", error);
      return { success: false, error: error.message };
    }
  });

  // 检查更新
  ipcMain.handle("check-for-updates", () => {
    if (app.isPackaged) {
      checkForUpdates(true); // 传入true表示手动检查
      return { success: true };
    }
    return { success: false, error: '开发模式下无法检查更新' };
  });

  // 注册快捷键
  globalShortcut.register("CommandOrControl+Shift+P", () => {
    if (mainWindow) {
      openExtensionByName(mainWindow);
    }
  });



  // 在生产环境中检查更新
  if (app.isPackaged) {
    // 延迟5秒检查更新，确保应用完全加载
    setTimeout(() => {
      checkForUpdates(false); // 传入false表示自动检查
    }, 5000);
  }

  // 应用启动后自动加载插件
  setTimeout(() => {
    if (mainWindow) {
      console.log("Auto loading extension...");
      // 检查是否已经加载了插件
      const loadedExtensions =
        mainWindow.webContents.session.getAllExtensions();
      const existingExtension = loadedExtensions.find(
        (ext) => ext.path === extensionPath
      );

      if (!existingExtension) {
        // 静默加载插件，不显示对话框
        silentLoadExtension(mainWindow);
      } else {
        console.log(`Extension already loaded, ID: ${existingExtension.id}`);
      }
    }
  }, 1500); // 延迟1.5秒等待窗口完全加载
});

// 当应用退出前注销所有快捷键
app.on("will-quit", () => {
  // 注销所有快捷键
  globalShortcut.unregisterAll();
});

// 关闭所有窗口时退出应用
app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    app.quit();
  }
});

app.on("activate", () => {
  // 在macOS上，当单击dock图标并且没有其他窗口打开时，
  // 通常在应用程序中重新创建一个窗口。
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});
