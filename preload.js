// 预加载脚本
const { contextBridge, ipcRenderer } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 加载插件
  loadExtension: () => ipcRenderer.invoke('load-extension'),
  
  // 获取已加载的插件列表
  getExtensions: () => ipcRenderer.invoke('get-extensions'),
  
  // 打开插件弹窗
  openExtensionPopup: () => ipcRenderer.invoke('open-extension-popup'),
  
  // 检查插件窗口状态
  checkPluginWindow: () => ipcRenderer.invoke('check-plugin-window'),
  
  // 聚焦插件窗口
  focusPluginWindow: () => ipcRenderer.invoke('focus-plugin-window'),

  // 添加更新状态监听
  onUpdateStatus: (callback) => {
    ipcRenderer.on('update-status', (event, data) => {
      callback(data);
    });
  },
  
  // 手动检查更新
  checkForUpdates: () => ipcRenderer.invoke('check-for-updates'),
  
  // 页面缩放功能
  zoomIn: () => ipcRenderer.send('zoom-in'),
  zoomOut: () => ipcRenderer.send('zoom-out')
});

// 在window加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
  console.log('简单直播 Electron应用已加载');
  
  // 等待页面加载完成后再创建按钮
  waitForPageLoad();
  
  // 监听页面变化，确保按钮总是可见
  setupMutationObserver();
  
  // 添加鼠标滚轮缩放功能
  setupZoomFeature();
});

// 设置缩放功能
function setupZoomFeature() {
  // 跟踪Ctrl键状态
  let isCtrlPressed = false;
  
  // 监听键盘按下事件
  window.addEventListener('keydown', (event) => {
    if (event.key === 'Control') {
      isCtrlPressed = true;
    }
  });
  
  // 监听键盘释放事件
  window.addEventListener('keyup', (event) => {
    if (event.key === 'Control') {
      isCtrlPressed = false;
    }
  });
  
  // 监听鼠标滚轮事件
  window.addEventListener('wheel', (event) => {
    // 只有在按下Ctrl键的情况下才处理缩放
    if (isCtrlPressed) {
      // 阻止默认行为（浏览器自带的缩放）
      event.preventDefault();
      
      // 根据滚轮方向决定是放大还是缩小
      if (event.deltaY < 0) {
        // 向上滚动，放大
        ipcRenderer.send('zoom-in');
      } else {
        // 向下滚动，缩小
        ipcRenderer.send('zoom-out');
      }
    }
  }, { passive: false }); // passive: false 允许我们调用 preventDefault()
}

// 等待页面完全加载后创建按钮
function waitForPageLoad() {
  // 检查document.body是否存在
  if (!document.body) {
    // 如果body不存在，稍后再试
    setTimeout(waitForPageLoad, 500);
    return;
  }
  
  // 检查页面是否已经包含我们的按钮
  const existingButton = document.getElementById('extension-floating-button');
  if (existingButton) {
    return;
  }
  
  // 页面内容可能还在加载，等待一会再添加按钮
  setTimeout(() => {
    try {
      createFloatingButton();
    } catch (error) {
      console.error('创建悬浮按钮失败:', error);
      // 失败后再次尝试
      setTimeout(() => {
        try {
          createFloatingButton();
        } catch (secondError) {
          console.error('创建悬浮按钮失败，已放弃尝试');
        }
      }, 3000);
    }
  }, 2000);
}

// 创建悬浮按钮
function createFloatingButton() {
  // 创建浮动按钮
  const button = document.createElement('div');
  button.id = 'extension-floating-button';
  button.style.position = 'fixed';
  button.style.right = '20px';
  button.style.bottom = '20px';
  button.style.width = '56px';
  button.style.height = '56px';
  button.style.borderRadius = '50%';
  button.style.backgroundColor = '#ff5722';
  button.style.color = 'white';
  button.style.display = 'flex';
  button.style.justifyContent = 'center';
  button.style.alignItems = 'center';
  button.style.cursor = 'pointer';
  button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  button.style.zIndex = '9999';
  button.style.fontSize = '28px';
  button.style.transition = 'all 0.3s ease';
  button.innerHTML = '🔌';
  button.title = '打开直播助手插件 (Ctrl+Shift+P)';
  
  // 悬停效果
  button.addEventListener('mouseover', () => {
    button.style.transform = 'scale(1.1)';
    button.style.boxShadow = '0 8px 20px rgba(0, 0, 0, 0.25)';
  });
  
  button.addEventListener('mouseout', () => {
    button.style.transform = 'scale(1)';
    button.style.boxShadow = '0 6px 16px rgba(0, 0, 0, 0.2)';
  });
  
  // 点击效果
  button.addEventListener('mousedown', () => {
    button.style.transform = 'scale(0.95)';
  });
  
  button.addEventListener('mouseup', () => {
    button.style.transform = 'scale(1.1)';
  });
  
  // 点击事件 - 直接使用IPC通信
  button.addEventListener('click', async () => {
    try {
      // 设置加载状态
      button.innerHTML = '⏳';
      
      // 先检查窗口状态
      const windowStatus = await ipcRenderer.invoke('check-plugin-window');
      
      if (windowStatus.exists) {
        // 窗口已存在，调用主进程恢复并聚焦窗口
        const focusResult = await ipcRenderer.invoke('focus-plugin-window');
        
        if (focusResult && focusResult.success) {
          // 成功聚焦窗口
          showTooltip('插件窗口已打开', button, false);
        } else {
          // 聚焦失败，可能窗口在检查后被关闭
          console.warn('无法聚焦插件窗口:', focusResult?.error);
          // 尝试重新打开窗口
          const result = await ipcRenderer.invoke('open-extension-popup');
          
          if (result && result.success) {
            showTooltip('插件已重新打开', button, false);
          } else {
            const errorMsg = result && result.error ? result.error : '打开插件失败';
            showTooltip(errorMsg, button, true);
          }
        }
        return;
      }
      
      // 直接通过IPC调用主进程
      const result = await ipcRenderer.invoke('open-extension-popup');
      
      if (result && result.success) {
        showTooltip('插件已打开', button, false);
      } else {
        const errorMsg = result && result.error ? result.error : '打开插件失败';
        console.error('打开插件窗口失败:', errorMsg);
        showTooltip(errorMsg, button, true);
      }
    } catch (error) {
      console.error('打开插件窗口出错:', error);
      showTooltip('打开插件窗口出错', button, true);
    }
  });
  
  // 添加到页面
  document.body.appendChild(button);
}

// 显示悬浮提示
function showTooltip(message, button, isError = false) {
  // 按钮状态
  button.innerHTML = isError ? '❌' : '🔌';
  
  // 创建提示元素
  const tooltip = document.createElement('div');
  tooltip.style.position = 'fixed';
  tooltip.style.right = '80px';
  tooltip.style.bottom = '20px';
  tooltip.style.padding = '8px 12px';
  tooltip.style.backgroundColor = isError ? 'rgba(220, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.7)';
  tooltip.style.color = 'white';
  tooltip.style.borderRadius = '4px';
  tooltip.style.fontSize = '14px';
  tooltip.style.zIndex = '10000';
  tooltip.innerText = message;
  document.body.appendChild(tooltip);
  
  // 设置淡出动画
  tooltip.style.transition = 'opacity 0.5s ease';
  
  // 延迟后移除
  setTimeout(() => {
    tooltip.style.opacity = '0';
    setTimeout(() => {
      if (document.body.contains(tooltip)) {
        document.body.removeChild(tooltip);
      }
      button.innerHTML = '🔌'; // 恢复按钮图标
    }, 500);
  }, isError ? 3000 : 2000);
  
  return tooltip;
}

// 设置DOM变化观察器
function setupMutationObserver() {
  // 创建一个观察器实例
  const observer = new MutationObserver((mutations) => {
    // 检查是否需要重新添加按钮
    const button = document.getElementById('extension-floating-button');
    if (!button && document.body) {
      createFloatingButton();
    }
  });
  
  // 配置观察选项
  const config = { 
    childList: true,   // 观察目标子节点的变化
    subtree: true      // 观察所有后代节点的变化
  };
  
  // 开始观察
  if (document.body) {
    observer.observe(document.body, config);
  } else {
    // 如果body还不存在，等待它存在后再观察
    const bodyCheckInterval = setInterval(() => {
      if (document.body) {
        observer.observe(document.body, config);
        clearInterval(bodyCheckInterval);
      }
    }, 500);
  }
} 