/**
 * HTTP请求模块
 * 封装fetch API，提供统一的接口进行网络请求
 */

// API基础URL
const baseUrl = "https://jd.jiajs.cn";
// const baseUrl = "http://192.168.5.209:9023";
// 导出为全局变量供其他脚本使用
window.baseUrl = baseUrl;

// 默认超时时间（毫秒）
const DEFAULT_TIMEOUT = 30000;

/**
 * 处理请求超时
 * @param {Promise} fetchPromise - fetch请求Promise
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise} 带有超时处理的Promise
 */
function timeoutPromise(fetchPromise, timeout) {
  return new Promise((resolve, reject) => {
    // 设置超时计时器
    const timeoutId = setTimeout(() => {
      reject(new Error("请求超时"));
    }, timeout);

    // 处理fetch请求结果
    fetchPromise
      .then((response) => {
        clearTimeout(timeoutId);
        resolve(response);
      })
      .catch((error) => {
        clearTimeout(timeoutId);
        reject(error);
      });
  });
}

/**
 * 检查响应状态并解析JSON
 * @param {Response} response - fetch响应对象
 * @returns {Promise} 解析后的响应数据
 */
async function handleResponse(response) {
  // 检查响应头中是否有新的token
  const newToken = response.headers.get("New-Token");
  if (newToken) {
    // 提取token值（如果格式为"Bearer xxx"，则只保留xxx部分）
    const tokenValue = newToken.startsWith("Bearer ")
      ? newToken.substring(7)
      : newToken;

    // 保存新的token到本地存储
    if (typeof window.saveUserData === "function") {
      window.saveUserData({ userToken: tokenValue });
      console.log("已更新用户Token");
    } else {
      chrome.storage.local.set({ userToken: tokenValue });
      console.log("已更新用户Token");
    }
  }

  // 处理HTTP错误状态码
  if (response.status === 401 && !newToken) {
    // 清除用户数据 - 使用window对象调用可能在其他模块中定义的函数
    if (typeof window.clearUserData === "function") {
      window.clearUserData();
    }

    // 显示登录页面 - 使用window对象调用可能在其他模块中定义的函数
    if (typeof window.showLoginPage === "function") {
      window.showLoginPage();
    }

    throw new Error("登录过期，请重新登录");
  } else if (!response.ok) {
    throw new Error(`请求异常: ${response.status}`);
  }

  // 解析响应JSON
  const data = await response.json();

  // 检查业务错误码
  if (data.code !== 0) {
    if (data.code === 1001) {
      // 打开续费弹窗 - 使用window对象调用可能在其他模块中定义的函数
      if (typeof window.showRenewalModal === "function") {
        window.showRenewalModal();
      }
    }
    throw new Error(data.msg || "请求失败");
  }

  return data;
}

/**
 * 获取认证头信息
 * @returns {Promise<Object>} 包含认证信息的请求头对象
 */
async function getAuthHeaders() {
  // 基础请求头
  const headers = {
    "Content-Type": "application/json",
  };

  // 如果存在获取用户数据的函数，则获取token - 使用window对象调用可能在其他模块中定义的函数
  if (typeof window.getUserData === "function") {
    try {
      const { userToken } = await window.getUserData(["userToken"]);
      if (userToken) {
        headers["Authorization"] = `Bearer ${userToken}`;
      }
    } catch (error) {
      console.error("获取用户Token失败:", error);
    }
  }

  return headers;
}

/**
 * HTTP请求客户端
 */
const http = {
  /**
   * 发送GET请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求配置选项
   * @returns {Promise} 响应数据
   */
  async get(url, options = {}) {
    const { timeout = DEFAULT_TIMEOUT, headers = {}, ...restOptions } = options;

    const requestUrl = url.startsWith("http") ? url : `${window.baseUrl}${url}`;
    const authHeaders = await getAuthHeaders();

    const fetchPromise = fetch(requestUrl, {
      method: "GET",
      headers: { ...authHeaders, ...headers },
      ...restOptions,
    });

    try {
      const response = await timeoutPromise(fetchPromise, timeout);
      return await handleResponse(response);
    } catch (error) {
      console.error(`GET请求失败: ${url}`, error);
      throw error;
    }
  },

  /**
   * 发送POST请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求体数据
   * @param {Object} options - 请求配置选项
   * @returns {Promise} 响应数据
   */
  async post(url, data = {}, options = {}) {
    const { timeout = DEFAULT_TIMEOUT, headers = {}, ...restOptions } = options;

    const requestUrl = url.startsWith("http") ? url : `${window.baseUrl}${url}`;
    const authHeaders = await getAuthHeaders();

    const fetchPromise = fetch(requestUrl, {
      method: "POST",
      headers: { ...authHeaders, ...headers },
      body: JSON.stringify(data),
      ...restOptions,
    });

    try {
      const response = await timeoutPromise(fetchPromise, timeout);
      return await handleResponse(response);
    } catch (error) {
      console.error(`POST请求失败: ${url}`, error);
      throw error;
    }
  },

  /**
   * 发送PUT请求
   * @param {string} url - 请求URL
   * @param {Object} data - 请求体数据
   * @param {Object} options - 请求配置选项
   * @returns {Promise} 响应数据
   */
  async put(url, data = {}, options = {}) {
    const { timeout = DEFAULT_TIMEOUT, headers = {}, ...restOptions } = options;

    const requestUrl = url.startsWith("http") ? url : `${window.baseUrl}${url}`;
    const authHeaders = await getAuthHeaders();

    const fetchPromise = fetch(requestUrl, {
      method: "PUT",
      headers: { ...authHeaders, ...headers },
      body: JSON.stringify(data),
      ...restOptions,
    });

    try {
      const response = await timeoutPromise(fetchPromise, timeout);
      return await handleResponse(response);
    } catch (error) {
      console.error(`PUT请求失败: ${url}`, error);
      throw error;
    }
  },

  /**
   * 发送DELETE请求
   * @param {string} url - 请求URL
   * @param {Object} options - 请求配置选项
   * @returns {Promise} 响应数据
   */
  async delete(url, options = {}) {
    const { timeout = DEFAULT_TIMEOUT, headers = {}, ...restOptions } = options;

    const requestUrl = url.startsWith("http") ? url : `${window.baseUrl}${url}`;
    const authHeaders = await getAuthHeaders();

    const fetchPromise = fetch(requestUrl, {
      method: "DELETE",
      headers: { ...authHeaders, ...headers },
      ...restOptions,
    });

    try {
      const response = await timeoutPromise(fetchPromise, timeout);
      return await handleResponse(response);
    } catch (error) {
      console.error(`DELETE请求失败: ${url}`, error);
      throw error;
    }
  },
};

// 导出http对象
window.http = http;
