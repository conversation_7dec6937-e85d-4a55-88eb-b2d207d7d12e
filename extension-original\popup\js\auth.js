/**
 * 认证模块
 * 处理用户Token验证、过期检查等认证相关功能
 */

// 获取DOM元素
const loginContainer = document.getElementById("login-container");
const tabContainer = document.querySelector(".tab-container");
const expireDateElement = document.getElementById("expireDate");



// 设置模块的缓存键名常量，导出为全局变量供其他脚本使用
const STORAGE_KEYS = {
  REPLY: "settingsReply",
  SEND: "settingsSend",
  PRODUCT_LIST: "settingsProductList",
  EXPLANATION: "settingsExplanation",
};
// 导出为全局变量
window.STORAGE_KEYS = STORAGE_KEYS;

// 轮询请求数据
window.AuthTimer = setInterval(()=>{
  checkAuthStatus()
}, 1000 * 30)

/**
 * 检查用户认证状态
 * 统一处理用户登录状态检查、token验证和过期检查
 * @returns {Promise} 返回一个Promise，resolve时会传递状态对象
 */
async function checkAuthStatus() {
  try {
    // 获取用户数据
    const { userToken, userPhone, expireDate } = await getUserData([
      "userToken",
      "userPhone",
      "expireDate"
    ]);
    
    // 调试输出获取到的数据
    // console.log("获取到的用户数据:", {
    //   hasToken: !!userToken,
    //   hasPhone: !!userPhone,
    //   expireDate: expireDate,
    // });

    // 如果没有token，直接显示登录页面
    if (!userToken) {
      showLoginPage();
      return { code: 1, msg: "未登录" };
    }

    // 暂时显示主功能页面和已存储的过期时间，提升用户体验
    showMainPage();

    if (expireDateElement && expireDate) {
      expireDateElement.textContent = `到期时间: ${expireDate}`;
      console.log("设置了到期时间显示:", expireDate);
    } else {
      console.log("无法设置到期时间", {
        expireDateElement: !!expireDateElement,
        expireDate,
      });
    }

    try {
      // 使用封装的http模块验证token状态
      const result = await http.get('/api/token/ok');
      console.log("检查Token状态", result);
      
      if (result.code === 1001) {
        // 打开续费弹窗
        showRenewalModal();
      }
      if (result.code !== 0) {
        throw new Error(result.msg);
      }
      // 一切正常，返回成功状态
      return { code: 0, msg: "认证成功", data: result.data };
    } catch (error) {
      console.error("检查Token状态出错:", error);
      // showLoginPage();
      stopAll();
      return { code: 2, msg: error.message };
    }
  } catch (err) {
    console.error("获取用户数据出错:", err);
    stopAll();
    showLoginPage();
    // 清除用户信息
    clearUserData();
    return { code: 3, msg: "获取用户数据出错" };
  }
}

window.checkAuthStatus = checkAuthStatus

// 关闭所有操作
function stopAll(){
  // 滚动回复的停止按钮
  let stop = document.getElementById("stop")
  let cancelQAButton = document.getElementById("cancelQAButton")
  let productStop = document.getElementById("productStop")
  

  // 判断是否是可点击状态（class包含disabled），如果是可点击状态，再执行点击操作
  if (!stop.classList.contains("disabled-btn")) {
    stop.click()
  }
  if (!cancelQAButton.classList.contains("disabled")) {
    cancelQAButton.click()
  }
  if (!productStop.classList.contains("disabled")) {
    productStop.click()
  }
 
  clearInterval(window.AuthTimer)
  window.AuthTimer = null
}

/**
 * 获取用户数据的Promise封装
 */
function getUserData(keys) {
  return new Promise((resolve) => {
    if (
      typeof chrome !== "undefined" &&
      chrome.storage &&
      chrome.storage.local
    ) {
      chrome.storage.local.get(keys, (result) => {
        resolve(result);
      });
    } else {
      // 使用localStorage作为备选
      const result = {};
      keys.forEach((key) => {
        result[key] = localStorage.getItem(key);
      });
      resolve(result);
    }
  });
}

/**
 * 显示登录页面
 */
function showLoginPage() {
  loginContainer.style.display = "flex";
  tabContainer.style.display = "none";
}

/**
 * 显示主功能页面
 */
function showMainPage() {
  loginContainer.style.display = "none";
  tabContainer.style.display = "flex";
}

/**
 * 清除用户数据
 */
function clearUserData() {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    chrome.storage.local.remove(["userToken", "userPhone", "expireDate"]);
  } else {
    localStorage.removeItem("userToken");
    localStorage.removeItem("userPhone");
    localStorage.removeItem("expireDate");
  }
}

/**
 * 保存用户数据
 */
function saveUserData(data) {
  if (typeof chrome !== "undefined" && chrome.storage && chrome.storage.local) {
    chrome.storage.local.set(data);
  } else {
    // 使用localStorage存储
    Object.keys(data).forEach((key) => {
      localStorage.setItem(key, data[key]);
    });
  }
}
