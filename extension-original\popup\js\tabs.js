document.addEventListener("DOMContentLoaded", function () {
  // 监听来自content script的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === "productPlayStopped") {
      console.log("收到商品讲解停止消息");
      // 更新商品讲解状态为停止
      chrome.storage.local.set({ productRunning: false }, () => {
        // 如果updateProductStatus函数可用，则调用它更新UI
        if (typeof window.updateProductStatus === "function") {
          window.updateProductStatus(false);
        }
      });
    }
  });

  // 获取所有选项卡按钮和内容区域
  const tabs = document.querySelectorAll(".tab");
  const tabContents = document.querySelectorAll(".tab-content");

  // 默认显示第一个选项卡
  tabs[0].classList.add("active");
  tabContents[0].classList.add("active");

  // 为每个选项卡添加点击事件
  tabs.forEach((tab) => {
    tab.addEventListener("click", function () {
      // 获取当前点击的选项卡索引
      const tabIndex = Array.from(tabs).indexOf(this);

      // 移除所有选项卡和内容区域的活动状态
      tabs.forEach((t) => t.classList.remove("active"));
      tabContents.forEach((content) => content.classList.remove("active"));

      // 激活当前点击的选项卡和对应的内容区域
      this.classList.add("active");
      tabContents[tabIndex].classList.add("active");

      // 如果点击的是商品讲解选项卡，则加载商品列表
      if (
        tabIndex === 2 &&
        !document.querySelector(".product-list-container").dataset.loaded
      ) {
        loadProductList();
      }
    });
  });

  // 初始化滚动回复输入框删除功能
  initContentDeleteButtons();

  // 为自动回复页的新增输入框按钮添加点击事件
  const addContentBtn = document.getElementById("addContentBtn");
  if (addContentBtn) {
    addContentBtn.addEventListener("click", function () {
      const csvList = document.getElementById("csvList");
      if (csvList) {
        // 创建group-item容器
        const groupItem = document.createElement("div");
        groupItem.className = "group-item";

        // 创建新的输入框
        const input = document.createElement("input");
        input.className = "contentInfo";
        input.type = "text";
        input.placeholder = "请输入内容";

        // 创建删除按钮
        const deleteBtn = document.createElement("span");
        deleteBtn.className = "delete-content";
        deleteBtn.textContent = "删除";

        // 添加删除按钮点击事件
        deleteBtn.addEventListener("click", function () {
          groupItem.remove();
          // 检查删除后的数量，如果只剩一个则隐藏其删除按钮
          updateDeleteButtonsVisibility();
        });

        // 将输入框和删除按钮添加到group-item
        groupItem.appendChild(input);
        groupItem.appendChild(deleteBtn);

        // 添加到csvList容器中
        csvList.appendChild(groupItem);

        // 更新删除按钮的可见性
        updateDeleteButtonsVisibility();
      }
    });
  }

  // 初始化滚动回复输入框删除按钮
  function initContentDeleteButtons() {
    // 处理页面加载时已有的输入框
    const csvList = document.getElementById("csvList");
    if (!csvList) return;

    // 获取现有的删除按钮
    const existingDeleteButtons = csvList.querySelectorAll(".delete-content");

    // 为每个删除按钮添加事件
    existingDeleteButtons.forEach((btn) => {
      btn.addEventListener("click", function () {
        // 获取父元素group-item
        const groupItem = this.closest(".group-item");
        if (!groupItem) return;

        // 删除当前item
        groupItem.remove();

        // 更新删除按钮的可见性
        updateDeleteButtonsVisibility();
      });
    });

    // 确保每个输入框都在group-item内并有删除按钮
    const inputsWithoutGroupItem = csvList.querySelectorAll(
      ".contentInfo:not(.group-item .contentInfo)"
    );
    if (inputsWithoutGroupItem.length > 0) {
      // 收集所有输入值
      const inputValues = Array.from(inputsWithoutGroupItem).map(
        (input) => input.value
      );

      // 移除所有不在group-item中的输入框
      inputsWithoutGroupItem.forEach((input) => input.remove());

      // 为每个值创建正确结构的group-item
      inputValues.forEach((value) => {
        // 创建group-item
        const groupItem = document.createElement("div");
        groupItem.className = "group-item";

        // 创建输入框
        const input = document.createElement("input");
        input.className = "contentInfo";
        input.type = "text";
        input.placeholder = "请输入内容";
        input.value = value || "";

        // 创建删除按钮
        const deleteBtn = document.createElement("span");
        deleteBtn.className = "delete-content";
        deleteBtn.textContent = "删除";

        // 添加删除按钮点击事件
        deleteBtn.addEventListener("click", function () {
          groupItem.remove();
          updateDeleteButtonsVisibility();
        });

        // 将输入框和删除按钮添加到group-item
        groupItem.appendChild(input);
        groupItem.appendChild(deleteBtn);

        // 添加到csvList容器中
        csvList.appendChild(groupItem);
      });

      // 如果没有输入框，创建一个默认的
      if (inputValues.length === 0) {
        // 创建group-item
        const groupItem = document.createElement("div");
        groupItem.className = "group-item";

        // 创建输入框
        const input = document.createElement("input");
        input.className = "contentInfo";
        input.type = "text";
        input.placeholder = "请输入内容";

        // 创建删除按钮
        const deleteBtn = document.createElement("span");
        deleteBtn.className = "delete-content";
        deleteBtn.textContent = "删除";
        deleteBtn.style.display = "none"; // 默认隐藏

        // 添加删除按钮点击事件
        deleteBtn.addEventListener("click", function () {
          // 不允许删除最后一个
          if (csvList.querySelectorAll(".group-item").length > 1) {
            groupItem.remove();
            updateDeleteButtonsVisibility();
          }
        });

        // 将输入框和删除按钮添加到group-item
        groupItem.appendChild(input);
        groupItem.appendChild(deleteBtn);

        // 添加到csvList容器中
        csvList.appendChild(groupItem);
      }

      // 更新删除按钮可见性
      updateDeleteButtonsVisibility();
    } else {
      // 如果没有找到输入框，也要检查删除按钮可见性
      updateDeleteButtonsVisibility();
    }
  }

  // 更新滚动回复输入框删除按钮的可见性
  function updateDeleteButtonsVisibility() {
    const csvList = document.getElementById("csvList");
    if (!csvList) return;

    const groupItems = csvList.querySelectorAll(".group-item");
    const deleteButtons = csvList.querySelectorAll(".delete-content");

    // 如果只有一个输入框，隐藏删除按钮；否则显示所有删除按钮
    if (groupItems.length <= 1) {
      deleteButtons.forEach((btn) => {
        btn.style.display = "none";
      });
    } else {
      deleteButtons.forEach((btn) => {
        btn.style.display = "";
      });
    }
  }

  // 为所有已有的删除按钮添加事件监听
  const deleteQABtns = document.querySelectorAll(".delete-qa");
  deleteQABtns.forEach((btn) => {
    btn.addEventListener("click", function () {
      const qalists = document.querySelectorAll(".qalist:not(.template)");
      // 如果只剩最后一个问答对，不允许删除
      if (qalists.length <= 1) {
        return;
      }
      this.closest(".qalist").remove();
      // 更新删除按钮可见性
      updateQADeleteButtonsVisibility();
    });
  });

  // 为"新增"按钮添加点击事件
  const addQAButton = document.getElementById("addQAButton");
  // console.log("新增按钮:", addQAButton);

  if (addQAButton) {
    addQAButton.onclick = function () {
      console.log("新增按钮被点击");

      // 创建新的问答列表项
      const newQAItem = document.createElement("div");
      newQAItem.className = "group qalist";
      newQAItem.innerHTML = `
                <input type="text" placeholder="请输入问题" />
                <span><i class="fas fa-exchange-alt"></i></span>
                <input type="text" placeholder="请输入自动回复内容"/>
                <span class="delete-qa">删除</span>
            `;

      // 为新添加的删除按钮添加事件监听
      const deleteBtn = newQAItem.querySelector(".delete-qa");
      deleteBtn.onclick = function () {
        const qalists = document.querySelectorAll(".qalist:not(.template)");
        // 如果只剩最后一个问答对，不允许删除
        if (qalists.length <= 1) {
          return;
        }
        newQAItem.remove();
        // 更新删除按钮可见性
        updateQADeleteButtonsVisibility();
      };

      // 获取父容器并插入到按钮前面
      const qaContainer = this.parentElement;
      qaContainer.insertBefore(newQAItem, this);

      // 更新删除按钮可见性
      updateQADeleteButtonsVisibility();
    };
  } else {
    console.error("未找到新增按钮，请检查HTML结构");
  }

  // 更新关键词回复删除按钮的可见性
  function updateQADeleteButtonsVisibility() {
    const qalists = document.querySelectorAll(".qalist:not(.template)");
    const deleteButtons = document.querySelectorAll(
      ".qalist:not(.template) .delete-qa"
    );

    // 如果只有一个问答对，隐藏删除按钮；否则显示所有删除按钮
    if (qalists.length <= 1) {
      deleteButtons.forEach((btn) => {
        btn.style.display = "none";
      });
    } else {
      deleteButtons.forEach((btn) => {
        btn.style.display = "";
      });
    }
  }

  // 页面加载时初始化删除按钮可见性
  document.addEventListener("DOMContentLoaded", function () {
    setTimeout(() => {
      updateDeleteButtonsVisibility();
      updateQADeleteButtonsVisibility();
    }, 100);
  });

  // 记录全选状态的变量
  let allSelected = false;

  // 模拟从后端获取商品列表并渲染
  function loadProductList() {
    const productContainer = document.querySelector(".product-list-container");

    // 设置加载状态
    productContainer.innerHTML =
      '<div class="loading-indicator"><i class="fas fa-spinner fa-spin"></i> 正在加载商品列表...</div>';

    // 模拟请求延迟
    setTimeout(() => {
      // 模拟从后端获取的商品数据
      let productsData = [];
      // 清空容器
      productContainer.innerHTML = "";
      chrome.storage.local.get(["productData", "selectProdIdList"], function (result) {
        if (result.productData && result.productData.length > 0) {
          productsData = result.productData;
          console.log("从存储中获取的商品数据:", productsData);
          // 渲染商品列表
          productsData.forEach((product) => {
            console.log(product);
            const productItem = document.createElement("div");
            productItem.className = "product-item";
            productItem.innerHTML = `
                            <input type="checkbox" id="product${product.id}" class="product-checkbox" data-product-id="${product.id}">
                            <label for="product${product.id}" class="product-label">
                                <img src="${product.img}" alt="${product.title}" class="product-image">
                                <span class="product-name">商品${product.id}：${product.title}</span>
                            </label>
                        `;
            productContainer.appendChild(productItem);
          });

          // 标记为已加载
          productContainer.dataset.loaded = "true";

          // 为选中的商品添加事件监听
          const checkboxes = document.querySelectorAll(".product-checkbox");
          checkboxes.forEach((checkbox) => {
            checkbox.addEventListener("change", updateSelectedProducts);
          });

          // 根据selectProdIdList设置选中状态
          if (result.selectProdIdList && result.selectProdIdList.length > 0) {
            console.log("已选商品ID列表:", result.selectProdIdList);
            checkboxes.forEach((checkbox) => {
              const productId = checkbox.dataset.productId;
              if (result.selectProdIdList.some(id => String(id) === String(productId))) {
                checkbox.checked = true;
              }
            });
          }

          // 初始化按钮事件绑定 - 确保在商品列表加载完成后执行
          setTimeout(() => {
            initButtonEvents();
            // 初始化已选商品计数
            updateSelectedProducts();
          }, 100);
        } else {
          console.error("未找到任何产品");
          // 显示无商品提示和获取商品按钮
          productContainer.innerHTML = `
                        <div class="no-product-message">
                            <p>未找到任何商品</p>
                            <button class="get-product-btn"><i class="fas fa-sync-alt"></i> 获取商品</button>
                        </div>
                    `;

          // 为获取商品按钮添加点击事件
          const getProductBtn = productContainer.querySelector(".get-product-btn");
          if (getProductBtn) {
            getProductBtn.addEventListener("click", async () => {
              // 向后台发送消息获取商品并重新加载
              await refreshProduct();
            });
          }

          // 标记为已加载但无数据
          productContainer.dataset.loaded = "true";
          productContainer.dataset.empty = "true";
        }
      });
    }, 800); // 模拟网络延迟
  }

  // 初始化按钮事件
  function initButtonEvents() {
    // 添加全选/取消全选切换按钮事件
    const toggleSelectBtn = document.querySelector(".toggle-select-btn");
    if (toggleSelectBtn) {
      // 移除之前可能存在的事件监听器
      toggleSelectBtn.replaceWith(toggleSelectBtn.cloneNode(true));
      // 重新获取按钮
      const newToggleBtn = document.querySelector(".toggle-select-btn");

      newToggleBtn.addEventListener("click", () => {
        // 添加按钮点击反馈
        if (newToggleBtn.classList.contains('disabled')) return;
        
        newToggleBtn.classList.add('disabled');
        const originalText = newToggleBtn.innerHTML;
        newToggleBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        newToggleBtn.disabled = true;
        
        // 延迟一点执行，让用户看到反馈效果
        setTimeout(() => {
          allSelected = !allSelected;

          // 修改所有复选框状态
          const checkboxes = document.querySelectorAll(".product-checkbox");
          if (checkboxes.length > 0) {
            checkboxes.forEach((cb) => {
              cb.checked = allSelected;
            });

            // 更新按钮文本和图标
            if (allSelected) {
              newToggleBtn.innerHTML = '<i class="fas fa-square"></i> 取消全选';
            } else {
              newToggleBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
            }

            updateSelectedProducts();
          } else {
            console.warn("没有找到商品复选框，请确认商品列表已加载");
            newToggleBtn.innerHTML = originalText;
          }
          
          // 恢复按钮状态
          newToggleBtn.classList.remove('disabled');
          newToggleBtn.disabled = false;
        }, 300);
      });
    }

    // 添加反选按钮事件
    const invertSelectBtn = document.querySelector(".invert-select-btn");
    if (invertSelectBtn) {
      // 移除之前可能存在的事件监听器
      invertSelectBtn.replaceWith(invertSelectBtn.cloneNode(true));
      // 重新获取按钮
      const newInvertBtn = document.querySelector(".invert-select-btn");

      newInvertBtn.addEventListener("click", () => {
        // 添加按钮点击反馈
        if (newInvertBtn.classList.contains('disabled')) return;
        
        newInvertBtn.classList.add('disabled');
        const originalText = newInvertBtn.innerHTML;
        newInvertBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
        newInvertBtn.disabled = true;
        
        // 延迟一点执行，让用户看到反馈效果
        setTimeout(() => {
          const checkboxes = document.querySelectorAll(".product-checkbox");
          if (checkboxes.length > 0) {
            checkboxes.forEach((cb) => {
              cb.checked = !cb.checked;
            });

            // 检查全选状态并更新按钮
            const toggleSelectBtn = document.querySelector(".toggle-select-btn");
            allSelected = Array.from(checkboxes).every((cb) => cb.checked);
            if (allSelected) {
              toggleSelectBtn.innerHTML =
                '<i class="fas fa-square"></i> 取消全选';
            } else {
              toggleSelectBtn.innerHTML =
                '<i class="fas fa-check-square"></i> 全选';
            }

            updateSelectedProducts();
          } else {
            console.warn("没有找到商品复选框，请确认商品列表已加载");
          }
          
          // 恢复按钮状态
          newInvertBtn.classList.remove('disabled');
          newInvertBtn.innerHTML = originalText;
          newInvertBtn.disabled = false;
        }, 300);
      });
    }

    // 添加刷新按钮事件
    const refreshProductBtn = document.querySelector(".refresh-product-btn");
    if (refreshProductBtn) {
      // 移除之前可能存在的事件监听器
      refreshProductBtn.replaceWith(refreshProductBtn.cloneNode(true));
      // 重新获取按钮
      const newRefreshBtn = document.querySelector(".refresh-product-btn");

      newRefreshBtn.addEventListener("click", async () => {
        // 如果按钮已禁用，直接返回
        if (newRefreshBtn.classList.contains('disabled')) return;
        
        // 重置全选状态
        allSelected = false;
        // 更新全选按钮的文本和图标
        const toggleSelectBtn = document.querySelector(".toggle-select-btn");
        if (toggleSelectBtn) {
          toggleSelectBtn.innerHTML =
            '<i class="fas fa-check-square"></i> 全选';
        }
        // 重新加载商品列表
        await refreshProduct();
      });
    }
  }

  // 确认按钮点击事件
  const confirmBtn = document.getElementById("productConfirm");
  if (confirmBtn) {
    confirmBtn.addEventListener("click", async () => {
      // 如果按钮处于禁用状态，直接返回
      if (confirmBtn.classList.contains('disabled')) {
        console.log("按钮已禁用，忽略点击");
        return;
      }
      
      // 立即禁用按钮，防止重复点击
      confirmBtn.classList.add('disabled');
      confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
      confirmBtn.disabled = true; // 添加HTML原生disabled属性
      
      // 调用auth.js的checkAuthStatus函数
      const res = await checkAuthStatus();
      if (res.code !== 0) {
        toast.error(res.msg);
        // 恢复按钮状态
        confirmBtn.classList.remove('disabled');
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
        confirmBtn.disabled = false;
        return;
      }
      const selectedProducts = [];
      document
        .querySelectorAll(".product-checkbox:checked")
        .forEach((checkbox) => {
          const productId = checkbox.dataset.productId;
          const productTitle =
            checkbox.nextElementSibling.querySelector(
              ".product-name"
            ).textContent;
          selectedProducts.push({ id: productId, title: productTitle });
        });

      // 保存当前选中的商品ID列表到缓存
      const selectedProductIds = selectedProducts.map(product => product.id);
      chrome.storage.local.set({ selectProdIdList: selectedProductIds }, function() {
        console.log("已更新选中的商品ID列表:", selectedProductIds);
      });

      // 获取并验证间隔时间
      const intervalTimeInput =
        document.querySelector("#productPlayTime").value;
      // 修改这里，使用 Number 转换，不提供默认值，以便捕获 0 值
      const intervalTime = Number(intervalTimeInput);

      if (selectedProducts.length === 0) {
        alert("请至少选择一个商品");
        // 恢复按钮状态
        confirmBtn.classList.remove('disabled');
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
        confirmBtn.disabled = false;
        return;
      }

      // 确保间隔时间不小于20秒，明确检查0值和NaN情况
      if (intervalTime < 20 || isNaN(intervalTime)) {
        alert("间隔时间至少为20秒");
        // 恢复按钮状态
        confirmBtn.classList.remove('disabled');
        confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
        confirmBtn.disabled = false;
        return;
      }
      
      // 将间隔时间保存到本地存储
      chrome.storage.local.set({ productPlayTime: intervalTime }, function() {
        console.log("已保存商品讲解间隔时间: " + intervalTime + "秒");
      });

      // 获取所有匹配的标签页
      chrome.tabs.query({ url: "https://eos.douyin.com/*" }, (tabs) => {
        if (tabs.length > 0) {
          let successResponses = 0;
          let failedResponses = 0;
          const totalTabs = tabs.length;
          
          // 记录每个标签页的响应
          tabs.forEach(tab => {
            console.log(`向标签页 ${tab.id} 发送商品讲解请求`);
            chrome.tabs.sendMessage(
              tab.id,
              {
                action: "playProduct",
                data: {
                  intervalTime: intervalTime,
                  products: selectedProducts,
                },
              },
              (response) => {
                // 处理内容脚本的响应
                if (chrome.runtime.lastError) {
                  console.error("消息发送失败:", chrome.runtime.lastError);
                  failedResponses++;
                } else if (response?.success) {
                  console.log("方法执行成功");
                  successResponses++;
                  toast.success("商品讲解已开始");
                  
                  // 设置商品讲解运行状态为true
                  chrome.storage.local.set({ productRunning: true }, () => {
                    // 如果updateProductStatus函数可用，则调用它更新UI
                    if (typeof window.updateProductStatus === "function") {
                      window.updateProductStatus(true);
                    }
                  });
                } else {
                  failedResponses++;
                }
                
                // 当所有标签页都有响应时恢复按钮状态
                if (successResponses + failedResponses >= totalTabs) {
                  // 恢复按钮状态
                  confirmBtn.classList.remove('disabled');
                  confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
                  confirmBtn.disabled = false;
                }
              }
            );
          });
          
          // 设置超时，如果3秒内没有收到所有响应，也恢复按钮状态
          setTimeout(() => {
            if (confirmBtn.classList.contains('disabled')) {
              confirmBtn.classList.remove('disabled');
              confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
              confirmBtn.disabled = false;
            }
          }, 3000);
        } else {
          toast.error("未找到抖音直播页面");
          // 恢复按钮状态
          confirmBtn.classList.remove('disabled');
          confirmBtn.innerHTML = '<i class="fas fa-check"></i> 确认';
          confirmBtn.disabled = false;
        }
      });
    });
  }

  // 停止按钮点击事件
  const stopBtn = document.getElementById("productStop");
  if (stopBtn) {
    stopBtn.addEventListener("click", () => {
      // 如果按钮处于禁用状态，直接返回
      if (stopBtn.classList.contains('disabled')) {
        console.log("停止按钮已禁用，忽略点击");
        return;
      }
      
      // 立即禁用按钮，防止重复点击
      stopBtn.classList.add('disabled');
      stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 停止中...';
      stopBtn.disabled = true;
      
      // 获取所有匹配的标签页
      chrome.tabs.query({ url: "https://eos.douyin.com/*" }, (tabs) => {
        if (tabs.length > 0) {
          let successResponses = 0;
          let failedResponses = 0;
          const totalTabs = tabs.length;
          
          // 向所有匹配的标签页发送消息
          tabs.forEach(tab => {
            chrome.tabs.sendMessage(
              tab.id,
              { action: "stopProduct" },
              (response) => {
                // 处理内容脚本的响应
                if (chrome.runtime.lastError) {
                  console.error("停止消息发送失败:", chrome.runtime.lastError);
                  failedResponses++;
                } else if (response?.success) {
                  console.log("停止方法执行成功");
                  toast.success("商品讲解已停止");
                  successResponses++;
                  
                  // 设置商品讲解运行状态为false
                  chrome.storage.local.set({ productRunning: false }, () => {
                    // 如果updateProductStatus函数可用，则调用它更新UI
                    if (typeof window.updateProductStatus === "function") {
                      window.updateProductStatus(false);
                    }
                  });
                } else {
                  failedResponses++;
                }
                
                // 当所有标签页都有响应时恢复按钮状态
                if (successResponses + failedResponses >= totalTabs) {
                  // 恢复按钮状态
                  stopBtn.classList.remove('disabled');
                  stopBtn.innerHTML = '<i class="fas fa-times"></i> 停止';
                  stopBtn.disabled = false;
                }
              }
            );
          });
          
          // 设置超时，如果3秒内没有收到所有响应，也恢复按钮状态
          setTimeout(() => {
            if (stopBtn.classList.contains('disabled')) {
              stopBtn.classList.remove('disabled');
              stopBtn.innerHTML = '<i class="fas fa-times"></i> 停止';
              stopBtn.disabled = false;
            }
          }, 3000);
        } else {
          toast.error("未找到抖音直播页面");
          // 恢复按钮状态
          stopBtn.classList.remove('disabled');
          stopBtn.innerHTML = '<i class="fas fa-times"></i> 停止';
          stopBtn.disabled = false;
        }
      });
    });
  }

  // 刷新商品列表函数
  async function refreshProduct(){
    // 获取刷新按钮
    const refreshBtn = document.querySelector(".refresh-product-btn");
    // 禁用刷新按钮
    if (refreshBtn) {
      refreshBtn.classList.add('disabled');
      refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 刷新中...';
      refreshBtn.disabled = true;
    }
    
    const productContainer = document.querySelector(".product-list-container");
    
    // 显示加载状态
    if (productContainer) {
      productContainer.innerHTML =
        '<div class="loading-indicator"><i class="fas fa-spinner fa-spin"></i> 正在刷新商品列表...</div>';
    }
    
    // 获取所有匹配的标签页
    chrome.tabs.query({ url: "https://eos.douyin.com/*" }, async (tabs) => {
      // 函数用于恢复按钮状态
      const resetRefreshBtn = () => {
        if (refreshBtn) {
          refreshBtn.classList.remove('disabled');
          refreshBtn.innerHTML = '<i class="fas fa-sync-alt"></i> 刷新';
          refreshBtn.disabled = false;
        }
      };
      
      if (tabs.length > 0) {
        try {
          // 向第一个匹配的标签页发送消息
          const response = await chrome.tabs.sendMessage(tabs[0].id, {action: "getProductList"});
          if(response && response.success){
            console.log("刷新成功，重新加载商品列表");
            // 重新加载商品列表
            loadProductList();
            // 恢复按钮状态
            resetRefreshBtn();
          } else {
            console.log("刷新失败");
            if (productContainer) {
              productContainer.innerHTML = `
                <div class="no-product-message">
                  <p>刷新商品失败</p>
                  <button class="get-product-btn"><i class="fas fa-sync-alt"></i> 重试</button>
                </div>
              `;
              
              // 为重试按钮添加点击事件
              const retryBtn = productContainer.querySelector(".get-product-btn");
              if (retryBtn) {
                retryBtn.addEventListener("click", async () => {
                  await refreshProduct();
                });
              }
            }
            // 恢复按钮状态
            resetRefreshBtn();
          }
        } catch (error) {
          console.error("刷新商品出错:", error);
          if (productContainer) {
            productContainer.innerHTML = `
              <div class="no-product-message">
                <p>刷新商品时出错</p>
                <button class="get-product-btn"><i class="fas fa-sync-alt"></i> 重试</button>
              </div>
            `;
            
            // 为重试按钮添加点击事件
            const retryBtn = productContainer.querySelector(".get-product-btn");
            if (retryBtn) {
              retryBtn.addEventListener("click", async () => {
                await refreshProduct();
              });
            }
          }
          // 恢复按钮状态
          resetRefreshBtn();
        }
      } else {
        if (productContainer) {
          productContainer.innerHTML = `
            <div class="no-product-message">
              <p>未找到抖音直播页面</p>
              <button class="get-product-btn"><i class="fas fa-sync-alt"></i> 重试</button>
            </div>
          `;
          
          // 为重试按钮添加点击事件
          const retryBtn = productContainer.querySelector(".get-product-btn");
          if (retryBtn) {
            retryBtn.addEventListener("click", async () => {
              await refreshProduct();
            });
          }
        }
        // 恢复按钮状态
        resetRefreshBtn();
      }
      
      // 设置超时，如果5秒内没有完成刷新操作，恢复按钮状态
      setTimeout(() => {
        resetRefreshBtn();
      }, 5000);
    });
  }

  // 更新已选商品计数
  function updateSelectedProducts() {
    const checkboxes = document.querySelectorAll(".product-checkbox");
    if (checkboxes.length === 0) {
      // 如果没有找到复选框，将计数设为0
      const countElement = document.querySelector(".selected-count span");
      if (countElement) {
        countElement.textContent = 0;
      }
      return;
    }

    const selectedCount = document.querySelectorAll(
      ".product-checkbox:checked"
    ).length;
    const countElement = document.querySelector(".selected-count span");
    const toggleBtn = document.querySelector(".toggle-select-btn");

    if (countElement) {
      countElement.textContent = selectedCount;
    }

    // 更新全选按钮的状态
    if (toggleBtn) {
      allSelected = selectedCount === checkboxes.length && selectedCount > 0;

      if (allSelected) {
        toggleBtn.innerHTML = '<i class="fas fa-square"></i> 取消全选';
      } else {
        toggleBtn.innerHTML = '<i class="fas fa-check-square"></i> 全选';
      }
    }
  }

  // 如果当前是第三个选项卡，立即加载商品列表
  if (
    Array.from(tabs).findIndex((tab) => tab.classList.contains("active")) === 2
  ) {
    loadProductList();
  } else {
    // 如果当前不是第三个选项卡，给按钮添加初始事件监听器
    // 这些按钮在切换到第三个选项卡并加载商品列表后会被正确重新初始化
    initButtonEvents();
  }
});
